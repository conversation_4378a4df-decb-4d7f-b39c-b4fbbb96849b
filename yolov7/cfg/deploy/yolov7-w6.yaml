# parameters
nc: 80  # number of classes
depth_multiple: 1.0  # model depth multiple
width_multiple: 1.0  # layer channel multiple

# anchors
anchors:
  - [ 19,27,  44,40,  38,94 ]  # P3/8
  - [ 96,68,  86,152,  180,137 ]  # P4/16
  - [ 140,301,  303,264,  238,542 ]  # P5/32
  - [ 436,615,  739,380,  925,792 ]  # P6/64

# yolov7-w6 backbone
backbone:
  # [from, number, module, args]
  [[-1, 1, ReOrg, []],  # 0
   [-1, 1, Conv, [64, 3, 1]],  # 1-P1/2
   
   [-1, 1, Conv, [128, 3, 2]],  # 2-P2/4
   [-1, 1, Conv, [64, 1, 1]],
   [-2, 1, Conv, [64, 1, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [[-1, -3, -5, -6], 1, Con<PERSON>, [1]],
   [-1, 1, Conv, [128, 1, 1]],  # 10
         
   [-1, 1, Conv, [256, 3, 2]],  # 11-P3/8
   [-1, 1, Conv, [128, 1, 1]],
   [-2, 1, Conv, [128, 1, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [[-1, -3, -5, -6], 1, Concat, [1]],
   [-1, 1, Conv, [256, 1, 1]],  # 19
         
   [-1, 1, Conv, [512, 3, 2]],  # 20-P4/16
   [-1, 1, Conv, [256, 1, 1]],
   [-2, 1, Conv, [256, 1, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [[-1, -3, -5, -6], 1, Concat, [1]],
   [-1, 1, Conv, [512, 1, 1]],  # 28
         
   [-1, 1, Conv, [768, 3, 2]],  # 29-P5/32
   [-1, 1, Conv, [384, 1, 1]],
   [-2, 1, Conv, [384, 1, 1]],
   [-1, 1, Conv, [384, 3, 1]],
   [-1, 1, Conv, [384, 3, 1]],
   [-1, 1, Conv, [384, 3, 1]],
   [-1, 1, Conv, [384, 3, 1]],
   [[-1, -3, -5, -6], 1, Concat, [1]],
   [-1, 1, Conv, [768, 1, 1]],  # 37
         
   [-1, 1, Conv, [1024, 3, 2]],  # 38-P6/64
   [-1, 1, Conv, [512, 1, 1]],
   [-2, 1, Conv, [512, 1, 1]],
   [-1, 1, Conv, [512, 3, 1]],
   [-1, 1, Conv, [512, 3, 1]],
   [-1, 1, Conv, [512, 3, 1]],
   [-1, 1, Conv, [512, 3, 1]],
   [[-1, -3, -5, -6], 1, Concat, [1]],
   [-1, 1, Conv, [1024, 1, 1]],  # 46
  ]

# yolov7-w6 head
head:
  [[-1, 1, SPPCSPC, [512]], # 47
  
   [-1, 1, Conv, [384, 1, 1]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [37, 1, Conv, [384, 1, 1]], # route backbone P5
   [[-1, -2], 1, Concat, [1]],
   
   [-1, 1, Conv, [384, 1, 1]],
   [-2, 1, Conv, [384, 1, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [[-1, -2, -3, -4, -5, -6], 1, Concat, [1]],
   [-1, 1, Conv, [384, 1, 1]], # 59
  
   [-1, 1, Conv, [256, 1, 1]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [28, 1, Conv, [256, 1, 1]], # route backbone P4
   [[-1, -2], 1, Concat, [1]],
   
   [-1, 1, Conv, [256, 1, 1]],
   [-2, 1, Conv, [256, 1, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [[-1, -2, -3, -4, -5, -6], 1, Concat, [1]],
   [-1, 1, Conv, [256, 1, 1]], # 71
   
   [-1, 1, Conv, [128, 1, 1]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [19, 1, Conv, [128, 1, 1]], # route backbone P3
   [[-1, -2], 1, Concat, [1]],
   
   [-1, 1, Conv, [128, 1, 1]],
   [-2, 1, Conv, [128, 1, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [-1, 1, Conv, [64, 3, 1]],
   [[-1, -2, -3, -4, -5, -6], 1, Concat, [1]],
   [-1, 1, Conv, [128, 1, 1]], # 83
      
   [-1, 1, Conv, [256, 3, 2]],
   [[-1, 71], 1, Concat, [1]],  # cat
   
   [-1, 1, Conv, [256, 1, 1]],
   [-2, 1, Conv, [256, 1, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [-1, 1, Conv, [128, 3, 1]],
   [[-1, -2, -3, -4, -5, -6], 1, Concat, [1]],
   [-1, 1, Conv, [256, 1, 1]], # 93
      
   [-1, 1, Conv, [384, 3, 2]],
   [[-1, 59], 1, Concat, [1]],  # cat
   
   [-1, 1, Conv, [384, 1, 1]],
   [-2, 1, Conv, [384, 1, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [-1, 1, Conv, [192, 3, 1]],
   [[-1, -2, -3, -4, -5, -6], 1, Concat, [1]],
   [-1, 1, Conv, [384, 1, 1]], # 103
      
   [-1, 1, Conv, [512, 3, 2]],
   [[-1, 47], 1, Concat, [1]],  # cat
   
   [-1, 1, Conv, [512, 1, 1]],
   [-2, 1, Conv, [512, 1, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [-1, 1, Conv, [256, 3, 1]],
   [[-1, -2, -3, -4, -5, -6], 1, Concat, [1]],
   [-1, 1, Conv, [512, 1, 1]], # 113
   
   [83, 1, Conv, [256, 3, 1]],
   [93, 1, Conv, [512, 3, 1]],
   [103, 1, Conv, [768, 3, 1]],
   [113, 1, Conv, [1024, 3, 1]],

   [[114,115,116,117], 1, Detect, [nc, anchors]],   # Detect(P3, P4, P5, P6)
  ]
