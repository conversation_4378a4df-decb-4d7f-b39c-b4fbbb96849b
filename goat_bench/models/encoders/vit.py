# adapted from: https://github.com/facebookresearch/mae/blob/main/models_vit.py
from functools import partial

import timm.models.vision_transformer
import torch
import torch.nn as nn


# fmt: off
class VisionTransformer(timm.models.vision_transformer.VisionTransformer):
    """ Vision Transformer with support for global average pooling
    """
    def __init__(self, use_fc_norm=False, global_pool=False, use_cls=False, mask_ratio=None, **kwargs):
        super(VisionTransformer, self).__init__(**kwargs)
        assert not (global_pool and use_cls)

        del self.head  # don't use prediction head

        self.use_fc_norm = use_fc_norm
        if self.use_fc_norm:
            norm_layer = kwargs['norm_layer']
            embed_dim = kwargs['embed_dim']
            self.fc_norm = norm_layer(embed_dim)

            del self.norm  # remove the original norm

        self.global_pool = global_pool
        self.use_cls = use_cls
        self.mask_ratio = mask_ratio

    def random_masking(self, x, mask_ratio):
        """
        Perform per-sample random masking by per-sample shuffling.
        Per-sample shuffling is done by argsort random noise.
        x: [N, L, D], sequence
        """
        N, L, D = x.shape  # batch, length, dim
        len_keep = int(L * (1 - mask_ratio))

        noise = torch.rand(N, L, device=x.device)  # noise in [0, 1]

        # sort noise for each sample
        ids_shuffle = torch.argsort(noise, dim=1)  # ascend: small is keep, large is remove
        ids_restore = torch.argsort(ids_shuffle, dim=1)

        # keep the first subset
        ids_keep = ids_shuffle[:, :len_keep]
        x_masked = torch.gather(x, dim=1, index=ids_keep.unsqueeze(-1).repeat(1, 1, D))

        # generate the binary mask: 0 is keep, 1 is remove
        mask = torch.ones([N, L], device=x.device)
        mask[:, :len_keep] = 0
        # unshuffle to get the binary mask
        mask = torch.gather(mask, dim=1, index=ids_restore)

        return x_masked, mask, ids_restore

    def forward_features(self, x):
        x = self.patch_embed(x)

        # add pos embed w/o cls token
        x = x + self.pos_embed[:, 1:, :]

        # masking: length -> length * mask_ratio
        if self.mask_ratio is not None:
            x, _, _ = self.random_masking(x, mask_ratio=self.mask_ratio)

        # append cls token
        cls_token = self.cls_token + self.pos_embed[:, :1, :]
        cls_tokens = cls_token.expand(x.shape[0], -1, -1)
        x = torch.cat((cls_tokens, x), dim=1)

        # apply Transformer blocks
        for blk in self.blocks:
            x = blk(x)
        if not self.use_fc_norm:
            x = self.norm(x)

        # global pooling or remove cls token
        if self.global_pool:
            x = x[:, 1:, :].mean(dim=1)  # global pool without cls token
        elif self.use_cls:
            x = x[:, 0]  # use cls token
        else:
            x = x[:, 1:]  # remove cls token

        # use fc_norm layer
        if self.use_fc_norm:
            x = self.fc_norm(x)

        return x

    def forward(self, x):
        return self.forward_features(x)


def vit_small_patch16(**kwargs):
    """ViT small as defined in the DeiT paper."""
    model = VisionTransformer(
        patch_size=16, embed_dim=384, depth=12, num_heads=6, mlp_ratio=4, qkv_bias=True,
        norm_layer=partial(nn.LayerNorm, eps=1e-6), **kwargs)
    return model


def vit_base_patch16(**kwargs):
    model = VisionTransformer(
        patch_size=16, embed_dim=768, depth=12, num_heads=12, mlp_ratio=4, qkv_bias=True,
        norm_layer=partial(nn.LayerNorm, eps=1e-6), **kwargs)
    return model


def vit_large_patch16(**kwargs):
    model = VisionTransformer(
        patch_size=16, embed_dim=1024, depth=24, num_heads=16, mlp_ratio=4, qkv_bias=True,
        norm_layer=partial(nn.LayerNorm, eps=1e-6), **kwargs)
    return model


def vit_huge_patch14(**kwargs):
    model = VisionTransformer(
        patch_size=14, embed_dim=1280, depth=32, num_heads=16, mlp_ratio=4, qkv_bias=True,
        norm_layer=partial(nn.LayerNorm, eps=1e-6), **kwargs)
    return model


def load_ovrl_v2(checkpoint: str, img_size: int) -> nn.Module:
    encoder = vit_base_patch16(
        img_size=img_size,
        use_fc_norm=False,
        global_pool=False,
        use_cls=False,
        mask_ratio=None,
        drop_path_rate=0.0,
    )
    weights = torch.load(checkpoint, map_location="cpu")["model"]
    orig_state_dict = encoder.state_dict()
    encoder.load_state_dict(
        {
            k: v
            for k, v in weights.items()
            if k in orig_state_dict
        }
    )
    return encoder

