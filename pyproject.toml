# Copyright [2023] Boston Dynamics AI Institute, Inc.

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "vlfm"
version = "0.1"
description = "Vision-Language Frontier Maps"
authors = [
    {name = "<PERSON><PERSON>", email = "nyo<PERSON><PERSON>@theaiinstitute.com"},
]
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    # "torch == 1.12.1",
    # "torchvision == 0.13.1",
    # "numpy == 1.26.4",
    # "flask >= 2.3.2",
    # "seaborn >= 0.12.2",  # required by yolov7
    # "open3d >= 0.17.0",
    # "transformers == 4.26.0",  # higher versions than 4.26.0 "break" BLIP-2 in LAVIS
    # "timm == 0.4.12",
    "frontier_exploration @ git+ssh://**************/naokiyokoyama/frontier_exploration.git",
    # "mobile_sam @ git+ssh://**************/ChaoningZhang/MobileSAM.git",
    # "depth_camera_filtering @ git+ssh://**************/naokiyokoyama/depth_camera_filtering",
    # "opencv-python == ********"
]

[project.optional-dependencies]
dev = [
    "pre-commit >= 3.1.1",
    "pytest >= 7.2.1",
    "pytest-cov >= 4.0.0",
]
habitat = [
    # "habitat-sim @ git+ssh://**************/facebookresearch/habitat-sim.git@v0.2.4",
    "habitat-baselines == 0.2.420230405",
    "habitat-lab == 0.2.420230405",
]
reality = [
    "spot_wrapper @ git+https://github.com/naokiyokoyama/bd_spot_wrapper.git",
    "bosdyn-client >= 3.3.2",
    "bosdyn-api >= 3.3.2",
    "six >= 1.16.0",
]

[project.urls]
"Homepage" = "theaiinstitute.com"
"GitHub" = "https://github.com/bdaiinstitute/vlfm"

[tool.setuptools.packages.find]
where = ["vlfm"]

[tool.ruff]
# Enable pycodestyle (`E`), Pyflakes (`F`), and import sorting (`I`)
select = ["E", "F", "I"]
ignore = []

# Allow autofix for all enabled rules (when `--fix`) is provided.
fixable = ["A", "B", "C", "D", "E", "F", "G", "I", "N", "Q", "S", "T", "W", "ANN", "ARG", "BLE", "COM", "DJ", "DTZ", "EM", "ERA", "EXE", "FBT", "ICN", "INP", "ISC", "NPY", "PD", "PGH", "PIE", "PL", "PT", "PTH", "PYI", "RET", "RSE", "RUF", "SIM", "SLF", "TCH", "TID", "TRY", "UP", "YTT"]
unfixable = []

# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "docker/ros",
]

# Same as Black.
line-length = 120

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

# Assume Python 3.9
target-version = "py39"

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]

[tool.ruff.mccabe]
# Unlike Flake8, default to a complexity level of 10.
max-complexity = 10

[tool.black]
line-length = 120
target-version = ['py39']
include = '\.pyi?$'
# `extend-exclude` is not honored when `black` is passed a file path explicitly,
# as is typical when `black` is invoked via `pre-commit`.
force-exclude = '''
/(
  docker/ros/.*
)/
'''

preview = true

[tool.coverage.run]
relative_files = true


# mypy configuration
[tool.mypy]
python_version = "3.9"
disallow_untyped_defs = true
ignore_missing_imports = true
explicit_package_bases = true
check_untyped_defs = true
strict_equality = true
warn_unreachable = true
warn_redundant_casts = true
no_implicit_optional = true
files = ['vlfm','test','scripts']
exclude = "^(docker|.*external|.*thirdparty|.*install|.*build|.*_experimental|.*_pb2.py|.*_pb2_grpc.py)"
