conda_env_name=fe
mamba create -n $conda_env_name python=3.7 cmake=3.14.0 -y
mamba install -n $conda_env_name \
  habitat-sim=0.2.3 headless pytorch cudatoolkit=11.3 \
  -c pytorch -c nvidia -c conda-forge -c aihabitat -y

# Install habitat-lab
git clone --branch v0.2.3 **************:facebookresearch/habitat-lab.git
cd habitat-lab
mamba activate $conda_env_name
pip install -e habitat-lab
pip install -e habitat-baselines
