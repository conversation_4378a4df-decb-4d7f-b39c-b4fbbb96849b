version: 2.1
gpu: &gpu
  machine:
    image: ubuntu-2004-cuda-11.4:202110-01
  resource_class: gpu.nvidia.medium
  environment:
    FPS_THRESHOLD: 900

orbs:
  codecov: codecov/codecov@3.2.3

jobs:
  python_lint:
    docker:
      - image: cimg/python:3.9.16
    steps:
      - checkout
      - run:
          name: setup
          command: |
              pip install black==23.1.0 --progress-bar off
              pip install "isort[pyproject]" numpy --progress-bar off
              pip install mypy==0.991 types-mock types-Pillow types-tqdm types-PyYAML --progress-bar off
              pip install -r habitat-lab/requirements.txt --progress-bar off
      - run:
          name: run black
          command: |
              black --exclude '/(\.eggs|\.git|\.hg|\.mypy_cache|\.nox|\.tox|\.venv|_build|buck-out|build|dist)|examples/tutorials/(colabs|nb_python)' habitat-lab/. habitat-baselines/. examples/. test/. --diff
              black --exclude '/(\.eggs|\.git|\.hg|\.mypy_cache|\.nox|\.tox|\.venv|_build|buck-out|build|dist)|examples/tutorials/(colabs|nb_python)' habitat-lab/. habitat-baselines/. examples/. test/. --check
      - run:
          name: run isort
          command: |
              isort --version
              isort habitat-lab/. habitat-baselines/. examples/. test/.  --diff
              isort habitat-lab/. habitat-baselines/. examples/. test/.  --check-only
      - run:
          name: run mypy
          command: |
              mypy --version
              mypy --exclude="^docs/|setup.py$"
      - run:
          name: run assert no files in habitat and habitat_baselines
          command: |
              if test -d  habitat ;           then  echo "folder habitat should not exist";           exit 1; fi
              if test -d  habitat_baselines ; then  echo "folder habitat_baselines should not exist"; exit 1; fi
  pre-commit:
    docker:
      - image: cimg/python:3.9.16
    working_directory: ~/repo/

    steps:
      - checkout
      - run:
          name: Combine precommit config and python versions for caching
          command: |
            cat .pre-commit-config.yaml > pre-commit-deps.txt
            python -VV >> pre-commit-deps.txt
      - restore_cache:
          keys:
          - v1-precommit-deps-{{ checksum "pre-commit-deps.txt" }}

      - run:
          name: Install Dependencies
          command: |
            pip install -U pip setuptools pre-commit
            # Install the hooks now so that they'll be cached
            pre-commit install-hooks
      - save_cache:
          paths:
            - ~/.cache/pre-commit
          key: v1-precommit-deps-{{ checksum "pre-commit-deps.txt" }}

      - run:
          name: Check Code Style using pre-commit
          command: |
            SKIP=clang-format,eslint pre-commit run --show-diff-on-failure --all-files
  install_and_test_ubuntu:
    <<: *gpu
    steps:
      - checkout:
          path: ./habitat-lab
      - run:
          name: Install cmake
          no_output_timeout: 5m
          command: |
              echo $(git ls-remote https://github.com/facebookresearch/habitat-sim.git HEAD | awk '{ print $1}') > ./hsim_sha
              wget https://github.com/Kitware/CMake/releases/download/v3.13.4/cmake-3.13.4-Linux-x86_64.sh
              sudo mkdir /opt/cmake
              sudo sh ./cmake-3.13.4-Linux-x86_64.sh --prefix=/opt/cmake --skip-license
              sudo ln -s /opt/cmake/bin/cmake /usr/local/bin/cmake
      - run:
          name: Install dependencies
          no_output_timeout: 20m
          command: |
              sudo apt-get update || true
              sudo apt-get install -y --no-install-recommends \
                  build-essential \
                  git \
                  curl \
                  vim \
                  ca-certificates \
                  libjpeg-dev \
                  libglm-dev \
                  libegl1-mesa-dev \
                  ninja-build \
                  xorg-dev \
                  freeglut3-dev \
                  pkg-config \
                  wget \
                  zip \
                  lcov\
                  libhdf5-dev \
                  libomp-dev \
                  unzip || true
              sudo apt install --allow-change-held-packages \
                  texlive-base \
                  texlive-latex-extra \
                  texlive-fonts-extra \
                  texlive-fonts-recommended
      - run:
          name: Check CUDA
          no_output_timeout: 20m
          background: true
          command: nvidia-smi
      # Restore Conda cache
      - restore_cache:
          keys:
            - conda-{{ checksum "habitat-lab/.circleci/config.yml" }}
      - run:
          name: Install conda and dependencies
          no_output_timeout: 20m
          command: |
              if [ ! -d ~/miniconda ]
              then
                curl -o ~/miniconda.sh -O  https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
                chmod +x ~/miniconda.sh
                bash ~/miniconda.sh -b -p $HOME/miniconda
                rm ~/miniconda.sh
                export PATH=$HOME/miniconda/bin:/usr/local/cuda/bin:$PATH
                conda create -y -n habitat python=3.9
                . activate habitat
                conda install -q -y -c conda-forge ninja ccache
                pip install pytest-sugar>=0.9.6 mock cython pygame flaky pytest pytest-mock pytest-cov psutil
              fi
      - run:
          name: Install pytorch
          no_output_timeout: 20m
          background: true
          command: |
              export PATH=$HOME/miniconda/bin:/usr/local/cuda/bin:$PATH
              . activate habitat;
              if [ ! -f ~/miniconda/pytorch_installed ]
              then
                # For whatever reason we have to install pytorch first. If it isn't
                # it installs the 1.4 cpuonly version. Which is no good.
                echo "Installing pytorch"
                conda install -y pytorch==1.12.1=py3.9_cuda11.3_cudnn8.3.2_0 torchvision==0.13.1=py39_cu113 cudatoolkit=11.3 -c pytorch -c nvidia
              fi
              touch ~/miniconda/pytorch_installed
              python -c 'import torch; print("Has cuda?", torch.cuda.is_available()); print("torch version:", torch.__version__);'
      - restore_cache:
          keys:
            - v1-habitat-sim-{{ checksum "./hsim_sha" }}
      - restore_cache:
          keys:
            - ccache-{{ arch }}-main
          paths:
            - /home/<USER>/.ccache
      - run:
          name: CCache initialization
          command: |
            export PATH=$HOME/miniconda/bin:/usr/local/cuda/bin:$PATH
            . activate habitat;
            ccache --show-stats
            ccache --zero-stats
            ccache --max-size=10.0G
      - run:
          name: Build and install habitat-sim
          no_output_timeout: 30m
          command: |
              if [ ! -d ./habitat-sim ]
              then
                git clone https://github.com/facebookresearch/habitat-sim.git --recursive
              fi
              export PATH=$HOME/miniconda/bin:/usr/local/cuda/bin:$PATH
              . activate habitat;
              cd habitat-sim
              pip install -r requirements.txt --progress-bar off
              pip install imageio imageio-ffmpeg
              python -u setup.py install --headless --with-cuda --bullet
      - run:
          name: Ccache stats
          when: always
          command: |
            export PATH=$HOME/miniconda/bin:/usr/local/cuda/bin:$PATH
            . activate habitat;
            ccache --show-stats
      - run:
          name: Download test data
          command: |
              export PATH=$HOME/miniconda/bin:/usr/local/cuda/bin:$PATH
              . activate habitat
              if [ ! -f ./habitat-sim/data/scene_datasets/habitat-test-scenes/van-gogh-room.glb ]
              then
                cd habitat-sim
                wget http://dl.fbaipublicfiles.com/habitat/habitat-test-scenes.zip
                unzip habitat-test-scenes.zip
                rm habitat-test-scenes.zip
                cd -
              fi
              if [ ! -f ./habitat-sim/data/robots/franka_panda/panda_arm.urdf ]
              then
                python -m habitat_sim.utils.datasets_download --uids franka_panda --data-path habitat-sim/data/
              fi
              if [ ! -f ./habitat-sim/data/robots/hab_spot_arm/urdf/hab_spot_arm.urdf ]
              then
                python -m habitat_sim.utils.datasets_download --uids hab_spot_arm --data-path habitat-sim/data/ --replace
              fi
              if [ ! -f ./habitat-sim/data/robots/hab_stretch/urdf/hab_stretch.urdf ]
              then
                python -m habitat_sim.utils.datasets_download --uids hab_stretch --data-path habitat-sim/data/
              fi
      - run:
          name: Download coda scene
          command: |
            if [ ! -f ./habitat-sim/data/scene_datasets/coda/coda.glb ]
            then
              cd habitat-sim
              wget --no-check-certificate 'https://docs.google.com/uc?export=download&id=1Pc-J6pZzXEd8RSeLM94t3iwO8q_RQ853' -O coda.zip
              unzip coda.zip -d data/scene_datasets
              rm coda.zip
            fi
      - run:
          name: Run sim benchmark
          command: |
              export PATH=$HOME/miniconda/bin:/usr/local/cuda/bin:$PATH
              . activate habitat; cd habitat-sim
              python examples/example.py --scene data/scene_datasets/habitat-test-scenes/van-gogh-room.glb --silent --test_fps_regression $FPS_THRESHOLD
      - save_cache:
          key: v1-habitat-sim-{{ checksum "./hsim_sha" }}
          background: true
          paths:
            - ./habitat-sim
      - save_cache:
          key: ccache-{{ arch }}-main
          background: true
          paths:
            - /home/<USER>/.ccache
      - run:
          name: Install api with baselines
          no_output_timeout: 20m
          command: |
              export PATH=$HOME/miniconda/bin:/usr/local/cuda/bin:$PATH
              . activate habitat; cd habitat-lab
              ln -s ../habitat-sim/data data
              while [ ! -f ~/miniconda/pytorch_installed ]; do sleep 2; done # wait for Pytorch
              pip install -e habitat-lab
              pip install -e habitat-baselines
      - save_cache:
          key: conda-{{ checksum "habitat-lab/.circleci/config.yml" }}
          background: true
          paths:
            - ~/miniconda
      - run:
          name: Run api tests
          no_output_timeout: 120m
          command: |
              export PATH=$HOME/miniconda/bin:/usr/local/cuda/bin:$PATH
              . activate habitat; cd habitat-lab
              export PYTHONPATH=.:$PYTHONPATH
              export MULTI_PROC_OFFSET=0 && export MAGNUM_LOG=quiet && export HABITAT_SIM_LOG=quiet
              python -m pytest --cov-report=xml --cov-report term  --cov=./
      - codecov/upload
      - run:
          name: Run baseline training tests
          no_output_timeout: 30m
          command: |
              export PATH=$HOME/miniconda/bin:/usr/local/cuda/bin:$PATH
              . activate habitat; cd habitat-lab
              export PYTHONPATH=.:$PYTHONPATH
              export MULTI_PROC_OFFSET=0 && export MAGNUM_LOG=quiet && export HABITAT_SIM_LOG=quiet
              # This is a flag that enables test_test_baseline_training to work
              export TEST_BASELINE_SMALL=1
              python -m pytest  test/test_baseline_training.py -s
      - run:
          name: Run Hab2.0 benchmark
          no_output_timeout: 30m
          command: |
              export PATH=$HOME/miniconda/bin:/usr/local/cuda/bin:$PATH
              . activate habitat; cd habitat-lab
              python -m habitat_sim.utils.datasets_download --uids hab2_bench_assets
              mkdir -p data/ep_datasets/
              cp data/hab2_bench_assets/bench_scene.json.gz data/ep_datasets/
              bash scripts/hab2_bench/bench_runner.sh
              python scripts/hab2_bench/plot_bench.py
              # Assert the SPS number are up to standard
              python scripts/hab2_bench/assert_bench.py
      - run:
          name: Build api documentation
          command: |
              export PATH=$HOME/miniconda/bin:/usr/local/cuda/bin:$PATH
              . activate habitat; cd habitat-lab
              # Download sim inventory for crosslinking (no need to build
              # the whole sim docs for that)
              # TODO: take it from github.com/facebookmicrosites/habitat-website
              #   instead
              mkdir -p ../habitat-sim/build/docs-public/habitat-sim
              curl -s https://aihabitat.org/docs/habitat-sim/objects.inv > ../habitat-sim/build/docs-public/habitat-sim/objects.inv
              cd docs
              conda install -y -c conda-forge doxygen
              conda install -y  jinja2 pygments docutils
              mkdir -p ../build/docs
              ./build-public.sh
      - run:
          name: Ensure non-editable mode works
          command: |
              cd habitat-lab
              export PATH=$HOME/miniconda/bin:/usr/local/cuda/bin:$PATH
              conda create -y -n non-editable-install python=3.9
              . activate non-editable-install
              conda install -y -c conda-forge -c aihabitat-nightly habitat-sim
              pip install habitat-lab/
              python -c 'import habitat; print("habitat version:", habitat.__version__)'
              pip install habitat-baselines/
              python -c 'import habitat_baselines; print("habitat_baselines version:", habitat_baselines.__version__)'
      - run: &build_sdist_and_bdist
          name: Build sdist and bdist
          command: |
              cd habitat-lab
              export PATH=$HOME/miniconda/bin:/usr/local/cuda/bin:$PATH
              conda create -y -n build-env python=3.9
              . activate build-env
              pip install --upgrade build
              python -m build -s -w -C--global-option=egg_info -C--global-option=--tag-date habitat-lab/
              python -m build -s -w -C--global-option=egg_info -C--global-option=--tag-date habitat-baselines/
      - run:
          name: Ensure sdist and bdist intall work
          command: |
              cd habitat-lab
              export PATH=$HOME/miniconda/bin:/usr/local/cuda/bin:$PATH
              conda create -y -n bdist-install python=3.9
              . activate bdist-install
              conda install -y -c conda-forge -c aihabitat-nightly habitat-sim
              conda create -n sdist-install --clone bdist-install
              # install from built distribution:
              . activate bdist-install
              pip install habitat-lab/dist/habitat_lab*.whl
              python -c 'import habitat; print("habitat version:", habitat.__version__)'
              pip install habitat-baselines/dist/habitat_baselines*.whl
              python -c 'import habitat_baselines; print("habitat_baselines version:", habitat_baselines.__version__)'
              # install from source distribution:
              . activate sdist-install
              pip install habitat-lab/dist/habitat-lab*.tar.gz
              python -c 'import habitat; print("habitat version:", habitat.__version__)'
              pip install habitat-baselines/dist/habitat-baselines*.tar.gz
              python -c 'import habitat_baselines; print("habitat_baselines version:", habitat_baselines.__version__)'
      - store_artifacts:
          path: habitat-lab/data/profile  # This is the benchmark profile
  pypi_deploy:
    <<: *gpu
    parameters:
      username_env_var:
        description: "Twine username environment variable name"
        type: env_var_name
        default: TESTPYPI_USERNAME
      password_env_var:
        description: "Twine password environment variable name"
        type: env_var_name
        default: TESTPYPI_PASSWORD
      repository:
        description: "Twine repository name (possible options: testpypi or pypi)"
        type: string
        default: "testpypi"
    steps:
      - checkout:
          path: ./habitat-lab
      - run:
          name: Install conda
          no_output_timeout: 20m
          command: |
              if [ ! -d ~/miniconda ]
              then
                curl -o ~/miniconda.sh -O  https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
                chmod +x ~/miniconda.sh
                bash ~/miniconda.sh -b -p $HOME/miniconda
                rm ~/miniconda.sh
              fi
      - run: *build_sdist_and_bdist
      - run:
          name: Deploy Habitat-Lab and Habitat-Baselines distributions to PyPI
          command: |
              cd habitat-lab
              export PATH=$HOME/miniconda/bin:/usr/local/cuda/bin:$PATH
              . activate build-env
              pip install --upgrade twine
              twine upload \
                --username ${<< parameters.username_env_var >>} \
                --password ${<< parameters.password_env_var >>} \
                --repository << parameters.repository >> \
                habitat-lab/dist/*
              twine upload \
                --username ${<< parameters.username_env_var >>} \
                --password ${<< parameters.password_env_var >>} \
                --repository << parameters.repository >> \
                habitat-baselines/dist/*

workflows:
  version: 2
  install_and_test:
    jobs:
      - pre-commit
      - python_lint
      - install_and_test_ubuntu
  testpypi_nightly:
    triggers:
      - schedule:
          cron: "0 7 * * *"
          filters:
            branches:
              only: main
    jobs:
      - pre-commit
      - python_lint
      - install_and_test_ubuntu
      - pypi_deploy:
          requires:
            - pre-commit
            - python_lint
            - install_and_test_ubuntu
          context:
            - pypi_context
  version_pipy_release:
    jobs:
      - pre-commit:
          filters: &version_filter
            tags:
              only: /^v[0-9]+(\.[0-9]+)*.*/ # v0.1.5-rc1
            branches:
              ignore: /.*/
      - python_lint:
          filters: *version_filter
      - install_and_test_ubuntu:
          filters: *version_filter
      - pypi_deploy:
          filters: *version_filter
          username_env_var: PYPI_USERNAME
          password_env_var: PYPI_PASSWORD
          repository: "pypi"
          requires:
            - pre-commit
            - python_lint
            - install_and_test_ubuntu
          context:
            - pypi_context
