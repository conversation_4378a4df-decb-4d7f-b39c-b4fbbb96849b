exclude: 'build|src/deps|src/obsolete'

default_language_version:
    python: python3

repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.2.0
    hooks:
    -   id: trailing-whitespace
    -   id: check-added-large-files
        args: ['--maxkb=2000']
    -   id: end-of-file-fixer
    -   id: debug-statements
    -   id: check-case-conflict
    -   id: check-docstring-first
    -   id: check-executables-have-shebangs
    -   id: check-merge-conflict
    -   id: check-toml
    -   id: check-yaml
    -   id: mixed-line-ending
        args: ['--fix=lf']

-   repo: https://github.com/macisamuele/language-formatters-pre-commit-hooks
    rev: v2.3.0
    hooks:
    -   id: pretty-format-ini
        args: [--autofix]
    -   id: pretty-format-toml
        args: [--autofix]
        additional_dependencies:
         - toml-sort==0.21.0

-   repo: https://github.com/pre-commit/pygrep-hooks
    rev: v1.9.0  # Use the ref you want to point at
    hooks:
    -   id: python-check-blanket-noqa
    # TODO: Add blanket type ignore check as well

-   repo: https://github.com/timothycrosley/isort
    rev: 5.11.5
    hooks:
    -   id: isort
        exclude: docs/|examples/tutorials/(colabs|nb_python)/(.*\.py|.*\.ipynb)$
        additional_dependencies: [toml]

-   repo: https://github.com/ambv/black
    rev: 23.1.0
    hooks:
    - id: black
      exclude: ^examples/tutorials/(nb_python|colabs)

-   repo: https://github.com/myint/autoflake
    rev: v1.4
    hooks:
    -   id: autoflake
        args: ['--expand-star-imports', '--ignore-init-module-imports', '--in-place']
        exclude: docs/

-   repo: https://github.com/PyCQA/flake8
    rev: 4.0.1
    hooks:
    -   id: flake8
        exclude: docs/
        additional_dependencies:
        - flake8-bugbear==22.4.25
        - flake8-builtins==1.5.3
        - flake8-comprehensions==3.8.0
        - flake8-return==1.1.3
        - flake8-simplify==0.18.2

-   repo: https://github.com/pre-commit/mirrors-mypy
    rev: v0.991
    hooks:
    -   id: mypy
        # https://github.com/python/mypy/issues/4008
        exclude: "^docs/|setup.py$"
        additional_dependencies:
         - attrs>=19.1.0
         - hydra-core>=1.2.0
         - numpy>=1.20.0
         - omegaconf>=2.2.3
         - types-mock
         - types-Pillow
         - types-tqdm
         - types-PyYAML

-   repo: https://github.com/kynan/nbstripout
    rev: 0.5.0
    hooks:
    -   id: nbstripout
        files: ".ipynb"

-   repo: https://github.com/mwouts/jupytext
    rev: v1.13.8
    hooks:
    -   id: jupytext
        files: '^examples/tutorials/(colabs|nb_python)/(.*\.py|.*\.ipynb)$'
        args: [--update-metadata, '{"jupytext":{"notebook_metadata_filter":"all", "cell_metadata_filter":"-all"}, "accelerator":"GPU"}', --set-formats, 'nb_python//py:percent,colabs//ipynb,', --pipe, black, --pipe, 'isort - --treat-comment-as-code "# %%"', --pipe-fmt, 'py:percent', --sync]
        additional_dependencies:
            - 'nbformat<=5.0.8'
            - black==23.1.0
            - isort

-   repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.8.0.4
    hooks:
    -   id: shellcheck

-   repo: https://github.com/AleksaC/circleci-cli-py
    rev: v0.1.17183
    hooks:
    -   id: circle-ci-validator
