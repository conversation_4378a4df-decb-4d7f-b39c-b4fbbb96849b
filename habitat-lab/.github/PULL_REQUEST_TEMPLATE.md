## Motivation and Context

<!--- Why is this change required? What problem does it solve? -->
<!--- Please link to an existing issue here if one exists. -->
<!--- (we recommend to have an existing issue for each pull request) -->

## How Has This Been Tested

<!--- Please describe here how your modifications have been tested. -->

## Types of changes

<!--- What types of changes does your code introduce? Please mark the title of your pull request with one of the following -->
- **\[Docs change\]** Addition or changes to the documentation
- **\[Refactoring\]** Large changes to the code that improve its functionality or performance
- **\[Dependency Upgrade\]** Upgrades one or several dependencies in habitat
- **\[Bug Fix\]** (non-breaking change which fixes an issue)
- **\[Development\]** A pull request that add new features to the [habitat-lab](/habitat-lab) task and environment codebase. Development Pull Requests must be small (less that 500 lines of code change), have unit testing, very extensive documentation and examples. These are typically new tasks, environments, sensors, etc... The review process for these Pull Request is longer because these changes will be maintained by our core team of developers, so make sure your changes are easy to understand!
- **\[Experiment\]** A pull request that add new features to the [habitat-baselines](/habitat-baselines/) training codebase. Experiments Pull Requests can be any size, must have smoke/integration tests and be isolated from the rest of the code. Your code additions should not rely on other habitat-baselines code. This is to avoid dependencies between different parts of habitat-baselines. You must also include a README that will document how to use your new feature or trainer. **You** will be the maintainer of this code, if the code becomes stale or is not supported often enough, we will eventually remove it.

## Checklist

<!--- Go over all the following points, and put an `x` in all the boxes that apply. -->
<!--- If you're unsure about any of these, don't hesitate to ask. We're here to help! -->
- [ ] My code follows the code style of this project.
- [ ] I have updated the documentation if required.
- [ ] I have read the [**CONTRIBUTING**](/CONTRIBUTING.md) document.
- [ ] I have completed my CLA (see **CONTRIBUTING**)
- [ ] I have added tests to cover my changes if required.
