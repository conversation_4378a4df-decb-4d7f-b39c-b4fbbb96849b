# @package _global_

defaults:
  - /habitat: habitat_config_base
  - /habitat/simulator/<EMAIL>.main_agent: depth_head_agent
  - /habitat/task/rearrange: place
  - /habitat/dataset/rearrangement: replica_cad
  - _self_

habitat:
  gym:
    obs_keys:
      - head_depth
      - obj_goal_sensor
      - joint
      - is_holding
      - relative_resting_position
  environment:
    max_episode_steps: 300
  simulator:
    type: RearrangeSim-v0
    additional_object_paths:
    - data/objects/ycb/configs/
    needs_markers: False
    concur_render: True
    auto_sleep: True
    agents:
      main_agent:
        radius: 0.3
        articulated_agent_urdf: ./data/robots/hab_fetch/robots/hab_suction.urdf
        articulated_agent_type: FetchSuctionRobot
    habitat_sim_v0:
      allow_sliding: False
      enable_physics: True
  dataset:
    data_path: data/datasets/replica_cad/rearrange/v1/{split}/rearrange_easy.json.gz
