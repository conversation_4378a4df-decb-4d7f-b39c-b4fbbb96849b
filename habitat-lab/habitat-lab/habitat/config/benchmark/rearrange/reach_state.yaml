# @package _global_

defaults:
  - /habitat: habitat_config_base
  - /habitat/simulator/<EMAIL>.main_agent: depth_head_agent
  - /habitat/task/rearrange: reach_state
  - /habitat/dataset/rearrangement: replica_cad
  - _self_

habitat:
  gym:
    obs_keys:
      - joint
      - relative_resting_position
    action_keys:
      - arm_action
  environment:
    max_episode_steps: 20
  simulator:
    type: RearrangeSim-v0
    concur_render: True
    auto_sleep: True
    agents:
      main_agent:
        radius: 0.3
        sim_sensors:
          head_depth_sensor:
            height: 32
            width: 32
    habitat_sim_v0:
      allow_sliding: False
      enable_physics: True
  dataset:
    data_path: data/datasets/replica_cad/rearrange/v1/{split}/empty_1k_100.json.gz
