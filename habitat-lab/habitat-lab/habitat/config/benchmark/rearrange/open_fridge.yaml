# @package _global_

defaults:
  - /habitat: habitat_config_base
  - /habitat/simulator/<EMAIL>.main_agent: depth_head_agent
  - /habitat/task/rearrange: open_fridge
  - /habitat/dataset/rearrangement: replica_cad
  - _self_

habitat:
  gym:
    obs_keys:
      - head_depth
      - joint
      - ee_pos
      - is_holding
      - relative_resting_position
  environment:
    max_episode_steps: 200
  simulator:
    type: RearrangeSim-v0
    additional_object_paths:
      - data/objects/ycb/configs/
    debug_render_goal: False
    concur_render: True
    auto_sleep: True
    agents:
      main_agent:
        radius: 0.3
        articulated_agent_urdf: ./data/robots/hab_fetch/robots/hab_suction.urdf
        articulated_agent_type: FetchSuctionRobot
    habitat_sim_v0:
      allow_sliding: False
      enable_physics: True
  dataset:
    data_path: data/datasets/replica_cad/rearrange/v1/{split}/in_fridge_1k_100.json.gz
