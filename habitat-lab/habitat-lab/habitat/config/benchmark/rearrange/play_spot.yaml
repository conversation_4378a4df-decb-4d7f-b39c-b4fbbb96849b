# @package _global_

defaults:
  - /habitat: habitat_config_base
  - /habitat/task/actions:
    - base_velocity_non_cylinder
  - /habitat/simulator/<EMAIL>.main_agent: rgbd_head_rgbdp_arm_agent
  - /habitat/task/rearrange: play
  - /habitat/dataset/rearrangement: replica_cad
  - _self_

# Config for empty task to explore the scene.
habitat:
  environment:
    max_episode_steps: 0
  task:
    actions:
      arm_action:
        type: "ArmAction"
        arm_controller: "ArmRelPosMaskAction"
        grip_controller: "GazeGraspAction"
        arm_joint_mask: [1,1,0,1,1,1,1]
        arm_joint_dimensionality: 7
        grasp_thresh_dist: 0.15
        disable_grip: False
        delta_pos_limit: 0.0125
        ee_ctrl_lim: 0.015
        gaze_distance_range: [0.01, 0.3]
        center_cone_angle_threshold: 20.0
        center_cone_vector: [0.0, 1.0, 0.0]
      base_velocity_non_cylinder:
        allow_dyn_slide: False
        # There is a collision if the difference between the clamped NavMesh position and target position
        # is more than than collision_threshold for any point
        collision_threshold: 1e-5
        # The x and y locations of the clamped NavMesh position
        navmesh_offset: [[0.0, 0.0], [0.25, 0.0], [-0.25, 0.0]]
        # If we allow the robot to move laterally
        enable_lateral_move: True
  simulator:
    type: RearrangeSim-v0
    seed: 100
    additional_object_paths:
      - "data/objects/ycb/configs/"
    agents:
      main_agent:
        radius: 0.3
        articulated_agent_urdf: data/robots/hab_spot_arm/urdf/hab_spot_arm.urdf
        articulated_agent_type: "SpotRobot"
        sim_sensors:
          arm_rgb_sensor:
            height: 480
            width: 640
            hfov: 47
          arm_depth_sensor:
            height: 224
            width: 171
            hfov: 55
          arm_panoptic_sensor:
            height: 224
            width: 171
            hfov: 55
    habitat_sim_v0:
      enable_physics: True
  dataset:
    data_path: data/datasets/replica_cad/rearrange/v1/{split}/all_receptacles_10k_1k.json.gz
