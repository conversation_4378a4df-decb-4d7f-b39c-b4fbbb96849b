# @package _global_

defaults:
  - /habitat: habitat_config_base
  - /habitat/task: vln_r2r
  - /habitat/simulator/<EMAIL>.main_agent: rgbd_agent
  - /habitat/dataset/vln: mp3d_r2r
  - _self_

habitat:
  environment:
    max_episode_steps: 500
  simulator:
    agents:
      main_agent:
        sim_sensors:
          rgb_sensor:
            width: 256
            height: 256
            hfov: 90
            type: HabitatSimRGBSensor
          depth_sensor:
            width: 256
            height: 256
    forward_step_size: 0.25
    turn_angle: 15
    habitat_sim_v0:
      gpu_device_id: 0
