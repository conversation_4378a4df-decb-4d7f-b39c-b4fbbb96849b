# @package habitat.task

defaults:
  - /habitat/task: task_config_base
  - /habitat/task/actions:
    - arm_action
    - base_velocity
    - rearrange_stop
  - /habitat/task/measurements:
    - object_to_goal_distance
    - articulated_agent_force
    - force_terminate
    - articulated_agent_colls
    - end_effector_to_object_distance
    - does_want_terminate
    - composite_success
    - bad_called_terminate
    - num_steps
    - did_violate_hold_constraint
    - move_objects_reward
    - composite_stage_goals
  - /habitat/task/lab_sensors:
    - relative_resting_pos_sensor
    - target_start_sensor
    - goal_sensor
    - joint_sensor
    - is_holding_sensor
    - end_effector_sensor
    - target_start_gps_compass_sensor
    - target_goal_gps_compass_sensor
    - localization_sensor
  - _self_

type: RearrangeCompositeTask-v0
reward_measure: move_obj_reward
success_measure: composite_success
success_reward: 100.0
end_on_success: True
constraint_violation_ends_episode: False
task_spec: tidy_house
actions:
  arm_action:
    grip_controller: MagicGraspAction
