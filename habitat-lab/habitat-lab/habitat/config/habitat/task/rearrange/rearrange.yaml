# @package habitat.task

defaults:
  - /habitat/task: task_config_base
  - /habitat/task/actions:
    - arm_action
    - base_velocity
    - rearrange_stop
  - /habitat/task/measurements:
    - articulated_agent_force
    - force_terminate
    - articulated_agent_colls
    - object_to_goal_distance
    - num_steps
    - end_effector_to_object_distance
    - does_want_terminate
    - composite_success
    - bad_called_terminate
    - did_violate_hold_constraint
    - move_objects_reward
  - /habitat/task/lab_sensors:
    - target_start_sensor
    - goal_sensor
    - joint_sensor
    - is_holding_sensor
    - end_effector_sensor
    - relative_resting_pos_sensor
    - target_start_gps_compass_sensor
    - target_goal_gps_compass_sensor
    - localization_sensor
  - _self_

type: RearrangeCompositeTask-v0
reward_measure: move_obj_reward
success_measure: composite_success
success_reward: 100.0
slack_reward: -0.01
end_on_success: True
constraint_violation_ends_episode: False
constraint_violation_drops_object: True
task_spec: rearrange
actions:
  arm_action:
    grip_controller: SuctionGraspAction
measurements:
  force_terminate:
    max_accum_force: 100000.0
    max_instant_force: 10000.0
  move_objects_reward:
    force_pen: 0.0
