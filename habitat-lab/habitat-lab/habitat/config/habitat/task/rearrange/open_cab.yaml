# @package habitat.task

defaults:
  - /habitat/task: task_config_base
  - /habitat/task/actions:
    - arm_action
    - base_velocity
    - rearrange_stop
  - /habitat/task/measurements:
    - articulated_agent_force
    - force_terminate
    - articulated_agent_colls
    - end_effector_to_rest_distance
    - ee_dist_to_marker
    - art_obj_at_desired_state
    - art_obj_state
    - does_want_terminate
    - art_obj_success
    - art_obj_reward
    - num_steps
    - did_violate_hold_constraint
  - /habitat/task/lab_sensors:
    - joint_sensor
    - is_holding_sensor
    - end_effector_sensor
    - relative_resting_pos_sensor
  - _self_

type: RearrangeOpenDrawerTask-v0
reward_measure: art_obj_reward
success_measure: art_obj_success
success_reward: 10.0
slack_reward: 0.0
success_state: 0.45
end_on_success: True
actions:
  arm_action:
    grip_controller: SuctionGraspAction
measurements:
  art_obj_at_desired_state:
    use_absolute_distance: False
  art_obj_reward:
    wrong_grasp_end: True
    grasp_reward: 5.0
    art_at_desired_state_reward: 5.0
    marker_dist_reward: 1.0
    constraint_violate_pen: 1.0
  force_terminate:
    max_accum_force: 20_000.0
    max_instant_force: 10_000.0
