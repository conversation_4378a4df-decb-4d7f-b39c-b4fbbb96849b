# @package habitat.task

defaults:
  - /habitat/task: task_config_base
  - /habitat/task/actions:
    - base_velocity
    - rearrange_stop
  - /habitat/task/measurements:
    - object_to_goal_distance
    - articulated_agent_force
    - force_terminate
    - articulated_agent_colls
    - rot_dist_to_goal
    - dist_to_goal
    - nav_to_pos_succ
    - does_want_terminate
    - rearrange_nav_to_obj_success
    - bad_called_terminate
    - rearrange_nav_to_obj_reward
    - num_steps
  - /habitat/task/lab_sensors:
    - nav_goal_sensor
    - joint_sensor
  - _self_

type: NavToObjTask-v0
reward_measure: nav_to_obj_reward
success_measure: nav_to_obj_success
success_reward: 10.0
slack_reward: -0.005
end_on_success: True
constraint_violation_ends_episode: False
constraint_violation_drops_object: True
measurements:
  force_terminate:
    max_accum_force: 10_000.0
    max_instant_force: 10_000.0
