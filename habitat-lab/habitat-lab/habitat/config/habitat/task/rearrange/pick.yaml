# @package habitat.task

defaults:
  - /habitat/task: task_config_base
  - /habitat/task/actions:
    - arm_action
    - base_velocity
  - /habitat/task/measurements:
    - articulated_agent_force
    - force_terminate
    - end_effector_to_rest_distance
    - end_effector_to_object_distance
    - did_pick_object
    - pick_success
    - pick_reward
    - did_violate_hold_constraint
    - num_steps
  - /habitat/task/lab_sensors:
    - target_start_sensor
    - joint_sensor
    - is_holding_sensor
    - end_effector_sensor
    - relative_resting_pos_sensor
  - _self_

type: RearrangePickTask-v0
base_angle_noise: 0.523599
constraint_violation_ends_episode: False
constraint_violation_drops_object: True
reward_measure: "pick_reward"
success_measure: "pick_success"
success_reward: 10.0
slack_reward: -0.005
end_on_success: True
actions:
  arm_action:
    grip_controller: SuctionGraspAction
measurements:
  force_terminate:
    max_accum_force: 10_000.0
    max_instant_force: 10_000.0
