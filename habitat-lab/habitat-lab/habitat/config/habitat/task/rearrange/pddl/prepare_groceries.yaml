init:
  - closed_cab(cab_push_point_4)
  - closed_cab(cab_push_point_5)
  - closed_cab(cab_push_point_6)
  - closed_cab(cab_push_point_7)
  - opened_fridge(fridge_push_point)

goal:
  expr_type: AND
  sub_exprs:
    - at(obj0_target|0,TARGET_obj0_target|0)
    - at(obj1_target|1,TARGET_obj1_target|1)
    - at(obj2_target|2,TARGET_obj2_target|2)
    - not_holding(robot_0)

objects:
  - name: obj0_target|0
    expr_type: movable_entity_type
  - name: TARGET_obj0_target|0
    expr_type: goal_entity_type

  - name: obj1_target|1
    expr_type: movable_entity_type
  - name: TARGET_obj1_target|1
    expr_type: goal_entity_type

  - name: obj2_target|2
    expr_type: movable_entity_type
  - name: TARGET_obj2_target|2
    expr_type: goal_entity_type

  - name: robot_0
    expr_type: robot_entity_type

stage_goals:
  stage_0_5:
    expr_type: AND
    sub_exprs:
      - holding(obj0_target|0, robot_0)
  stage_1:
    expr_type: AND
    sub_exprs:
      - at(obj0_target|0,TARGET_obj0_target|0)
      - not_holding(robot_0)
  stage_1_5:
    expr_type: AND
    sub_exprs:
      - at(obj0_target|0,TARGET_obj0_target|0)
      - holding(obj1_target|1, robot_0)
  stage_2:
    expr_type: AND
    sub_exprs:
      - at(obj0_target|0,TARGET_obj0_target|0)
      - at(obj1_target|1,TARGET_obj1_target|1)
      - not_holding(robot_0)
  stage_2_5:
    expr_type: AND
    sub_exprs:
      - at(obj0_target|0,TARGET_obj0_target|0)
      - at(obj1_target|1,TARGET_obj1_target|1)
      - holding(obj2_target|2, robot_0)
  stage_3:
    expr_type: AND
    sub_exprs:
      - at(obj0_target|0,TARGET_obj0_target|0)
      - at(obj1_target|1,TARGET_obj1_target|1)
      - at(obj2_target|2,TARGET_obj2_target|2)

solution:
  - nav(obj0_target|0, robot_0)
  - pick(obj0_target|0, robot_0)
  - nav(TARGET_obj0_target|0, robot_0)
  - place(obj0_target|0,TARGET_obj0_target|0, robot_0)

  - nav(obj1_target|1, robot_0)
  - pick(obj1_target|1, robot_0)
  - nav(TARGET_obj1_target|1, robot_0)
  - place(obj1_target|1,TARGET_obj1_target|1, robot_0)

  - nav(obj2_target|2, robot_0)
  - pick(obj2_target|2, robot_0)
  - nav(TARGET_obj2_target|2, robot_0)
  - place(obj2_target|2,TARGET_obj2_target|2, robot_0)
