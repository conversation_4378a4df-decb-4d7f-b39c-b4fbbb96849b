objects:
  - name: any_targets|0
    expr_type: movable_entity_type
  - name: TARGET_any_targets|0
    expr_type: goal_entity_type
  - name: any_targets|1
    expr_type: movable_entity_type
  - name: TARGET_any_targets|1
    expr_type: goal_entity_type
  - name: any_targets|2
    expr_type: movable_entity_type
  - name: TARGET_any_targets|2
    expr_type: goal_entity_type
  - name: any_targets|3
    expr_type: movable_entity_type
  - name: TARGET_any_targets|3
    expr_type: goal_entity_type
  - name: any_targets|4
    expr_type: movable_entity_type
  - name: TARGET_any_targets|4
    expr_type: goal_entity_type
  - name: robot_0
    expr_type: robot_entity_type


init:
  - closed_fridge(fridge_push_point)


goal:
  expr_type: AND
  sub_exprs:
    - at(any_targets|0,TARGET_any_targets|0)
    - at(any_targets|1,TARGET_any_targets|1)
    - at(any_targets|2,TARGET_any_targets|2)
    - at(any_targets|3,TARGET_any_targets|3)
    - at(any_targets|4,TARGET_any_targets|4)
    - not_holding(robot_0)

stage_goals:
  stage_0_5:
    expr_type: AND
    sub_exprs:
      - holding(any_targets|0, robot_0)
  stage_1:
    expr_type: AND
    sub_exprs:
      - at(any_targets|0,TARGET_any_targets|0)
      - not_holding(robot_0)
  stage_1_5:
    expr_type: AND
    sub_exprs:
      - at(any_targets|0,TARGET_any_targets|0)
      - holding(any_targets|1, robot_0)
  stage_2:
    expr_type: AND
    sub_exprs:
      - at(any_targets|0,TARGET_any_targets|0)
      - at(any_targets|1,TARGET_any_targets|1)
      - not_holding(robot_0)
  stage_2_5:
    expr_type: AND
    sub_exprs:
      - at(any_targets|0,TARGET_any_targets|0)
      - at(any_targets|1,TARGET_any_targets|1)
      - holding(any_targets|2, robot_0)
  stage_3:
    expr_type: AND
    sub_exprs:
      - at(any_targets|0,TARGET_any_targets|0)
      - at(any_targets|1,TARGET_any_targets|1)
      - at(any_targets|2,TARGET_any_targets|2)
      - not_holding(robot_0)
  stage_3_5:
    expr_type: AND
    sub_exprs:
      - at(any_targets|0,TARGET_any_targets|0)
      - at(any_targets|1,TARGET_any_targets|1)
      - at(any_targets|2,TARGET_any_targets|2)
      - holding(any_targets|3, robot_0)
  stage_4:
    expr_type: AND
    sub_exprs:
      - at(any_targets|0,TARGET_any_targets|0)
      - at(any_targets|1,TARGET_any_targets|1)
      - at(any_targets|2,TARGET_any_targets|2)
      - at(any_targets|3,TARGET_any_targets|3)
      - not_holding(robot_0)
  stage_4_5:
    expr_type: AND
    sub_exprs:
      - at(any_targets|0,TARGET_any_targets|0)
      - at(any_targets|1,TARGET_any_targets|1)
      - at(any_targets|2,TARGET_any_targets|2)
      - at(any_targets|3,TARGET_any_targets|3)
      - at(any_targets|4,TARGET_any_targets|4)

solution:
  - nav(any_targets|0, robot_0)
  - pick(any_targets|0, robot_0)
  - nav(TARGET_any_targets|0, robot_0)
  - place(any_targets|0,TARGET_any_targets|0, robot_0)
  - nav(any_targets|1, robot_0)
  - pick(any_targets|1, robot_0)
  - nav(TARGET_any_targets|1, robot_0)
  - place(any_targets|1,TARGET_any_targets|1, robot_0)
  - nav(any_targets|2, robot_0)
  - pick(any_targets|2, robot_0)
  - nav(TARGET_any_targets|2, robot_0)
  - place(any_targets|2,TARGET_any_targets|2, robot_0)
  - nav(any_targets|3, robot_0)
  - pick(any_targets|3, robot_0)
  - nav(TARGET_any_targets|3, robot_0)
  - place(any_targets|3,TARGET_any_targets|3, robot_0)
  - nav(any_targets|4, robot_0)
  - pick(any_targets|4, robot_0)
  - nav(TARGET_any_targets|4, robot_0)
  - place(any_targets|4,TARGET_any_targets|4, robot_0)
