---
dataset_path: "data/replica_cad/replicaCAD.scene_dataset_config.json"
additional_object_paths:
  - "data/objects/ycb/configs/"
scene_sets:
  -
    name: "scene_train_split"
    included_substrings:
      - "v3_sc0_staging"
      - "v3_sc1_staging"
      - "v3_sc2_staging"
    excluded_substrings: []
  -
    name: "scene_val_split"
    included_substrings:
      - "v3_sc3_staging"
    excluded_substrings: []
  -
    name: "scene_test_split"
    included_substrings:
      - "v3_sc4_staging"
    excluded_substrings: []

object_sets:
  -
    name: "kitchen"
    included_substrings:
      - "002_master_chef_can"
    excluded_substrings: []

receptacle_sets:
  -
    name: "inside_drawer"
    included_object_substrings:
      - ""
    excluded_object_substrings: []
    included_receptacle_substrings:
      - receptacle_aabb_drawer_left_bottom_frl_apartment_kitchen_counter
      - receptacle_aabb_drawer_left_top_frl_apartment_kitchen_counter
      - receptacle_aabb_drawer_middle_bottom_frl_apartment_kitchen_counter
      - receptacle_aabb_drawer_middle_middle_frl_apartment_kitchen_counter
      - receptacle_aabb_drawer_middle_top_frl_apartment_kitchen_counter
      - receptacle_aabb_drawer_right_bottom_frl_apartment_kitchen_counter
      - receptacle_aabb_drawer_right_top_frl_apartment_kitchen_counter
    excluded_receptacle_substrings: []

scene_sampler:
  type: "subset"
  params:
    scene_sets: ["scene_train_split", "scene_val_split"]

object_samplers:
  -
    name: "kitchen_counter"
    type: "uniform"
    params:
      object_sets: ["kitchen"]
      receptacle_sets: ["inside_drawer"]
      num_samples: [1, 1]
      orientation_sampling: "up"

object_target_samplers: []
markers:
  - name: "cab_push_point_7"
    type: "articulated_object"
    params:
      offset: [0.3,0.0,0]
      link: "drawer1_top"
      object: "kitchen_counter_:0000"
  - name: "cab_push_point_6"
    type: "articulated_object"
    params:
      offset: [0.3,0.0,0]
      link: "drawer2_top"
      object: "kitchen_counter_:0000"
  - name: "cab_push_point_5"
    type: "articulated_object"
    params:
      offset: [0.3,0.0,0]
      link: "drawer3"
      object: "kitchen_counter_:0000"
  - name: "cab_push_point_4"
    type: "articulated_object"
    params:
      offset: [0.3,0.0,0]
      link: "drawer4"
      object: "kitchen_counter_:0000"
  - name: "fridge_push_point"
    type: "articulated_object"
    params:
      offset: [0.10,-0.62,0.2]
      link: "top_door"
      object: "fridge_:0000"
