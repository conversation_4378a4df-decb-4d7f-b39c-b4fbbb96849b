---
dataset_path: "data/replica_cad/replicaCAD.scene_dataset_config.json"
additional_object_paths:
  - "data/objects/ycb/configs/"
scene_sets:
  -
    name: "scene_train_split"
    included_substrings:
      - "v3_sc0_staging"
      - "v3_sc1_staging"
      - "v3_sc2_staging"
    excluded_substrings: []
  -
    name: "scene_val_split"
    included_substrings:
      - "v3_sc3_staging"
    excluded_substrings: []
  -
    name: "scene_test_split"
    included_substrings:
      - "v3_sc4_staging"
    excluded_substrings: []


object_sets:
  -
    name: "kitchen"
    included_substrings:
      - "002_master_chef_can"
      - "003_cracker_box"
      - "004_sugar_box"
      - "005_tomato_soup_can"
      - "007_tuna_fish_can"
      - "008_pudding_box"
      - "009_gelatin_box"
      - "010_potted_meat_can"
      - "024_bowl"
    excluded_substrings: []

receptacle_sets:
  -
    name: "counter"
    included_object_substrings:
      - "kitchen_counter"
    excluded_object_substrings: []
    included_receptacle_substrings:
      - "receptacle_aabb_counter_left_kitchen_counter"
      - "receptacle_aabb_counter_right_kitchen_counter"
    excluded_receptacle_substrings: []

scene_sampler:
  type: "single"
  params:
    scene_sets: ["scene_train_split", "scene_val_split"]

object_samplers: []
object_target_samplers: []

markers:
  - name: "cab_push_point_7"
    type: "articulated_object"
    params:
      offset: [0.3,0.0,0]
      link: "drawer1_top"
      object: "kitchen_counter_:0000"
  - name: "cab_push_point_6"
    type: "articulated_object"
    params:
      offset: [0.3,0.0,0]
      link: "drawer2_top"
      object: "kitchen_counter_:0000"
  - name: "cab_push_point_5"
    type: "articulated_object"
    params:
      offset: [0.3,0.0,0]
      link: "drawer3"
      object: "kitchen_counter_:0000"
  - name: "cab_push_point_4"
    type: "articulated_object"
    params:
      offset: [0.3,0.0,0]
      link: "drawer4"
      object: "kitchen_counter_:0000"
  - name: "fridge_push_point"
    type: "articulated_object"
    params:
      offset: [0.10,-0.62,0.2]
      link: "top_door"
      object: "fridge_:0000"
