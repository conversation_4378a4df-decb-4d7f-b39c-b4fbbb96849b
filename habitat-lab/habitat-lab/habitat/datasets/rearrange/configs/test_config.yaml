---
dataset_path: "data/replica_cad/replicaCAD.scene_dataset_config.json"
additional_object_paths:
  - "data/objects/ycb/configs/"
scene_sets:
  -
    name: "v3_sc"
    included_substrings:
      - "v3_sc"
    excluded_substrings: []
    comment: "This set (v3_sc) selects all 105 ReplicaCAD variations with static furniture."

object_sets:
  -
    name: "kitchen"
    included_substrings:
      - "002_master_chef_can"
      - "003_cracker_box"
      - "004_sugar_box"
      - "005_tomato_soup_can"
      - "007_tuna_fish_can"
      - "008_pudding_box"
      - "009_gelatin_box"
      - "010_potted_meat_can"
      - "024_bowl"
    excluded_substrings: []

receptacle_sets:
  -
    name: "counter"
    included_object_substrings:
      - "kitchen_counter"
    excluded_object_substrings: []
    included_receptacle_substrings:
      - "receptacle_aabb_counter_left_kitchen_counter"
      - "receptacle_aabb_counter_right_kitchen_counter"
    excluded_receptacle_substrings: []

scene_sampler:
  type: "subset"
  params:
    scene_sets: ["v3_sc"]
  comment: "Samples from ReplicaCAD 105 variations with static furniture."


object_samplers:
  -
    name: "kitchen_counter"
    type: "uniform"
    params:
      object_sets: ["kitchen"]
      receptacle_sets: ["counter"]
      num_samples: [1, 1]
      orientation_sampling: "up"

object_target_samplers:
  -
    name: "kitchen_counter_targets"
    type: "uniform"
    params:
      object_samplers: ["kitchen_counter"]
      receptacle_sets: ["counter"]
      num_samples: [1, 1]
      orientation_sampling: "up"
markers:
  - name: "cab_push_point_7"
    type: "articulated_object"
    params:
      offset: [0.3,0.0,0]
      link: "drawer1_top"
      object: "kitchen_counter_:0000"
  - name: "cab_push_point_6"
    type: "articulated_object"
    params:
      offset: [0.3,0.0,0]
      link: "drawer2_top"
      object: "kitchen_counter_:0000"
  - name: "cab_push_point_5"
    type: "articulated_object"
    params:
      offset: [0.3,0.0,0]
      link: "drawer3"
      object: "kitchen_counter_:0000"
  - name: "cab_push_point_4"
    type: "articulated_object"
    params:
      offset: [0.3,0.0,0]
      link: "drawer4"
      object: "kitchen_counter_:0000"
  - name: "fridge_push_point"
    type: "articulated_object"
    params:
      offset: [0.10,-0.62,0.2]
      link: "top_door"
      object: "fridge_:0000"
