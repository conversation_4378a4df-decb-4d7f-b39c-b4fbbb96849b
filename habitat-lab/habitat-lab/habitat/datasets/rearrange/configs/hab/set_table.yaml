---
dataset_path: "data/replica_cad/replicaCAD.scene_dataset_config.json"
additional_object_paths:
  - "data/objects/ycb/configs/"
scene_sets:
  -
    name: "scene_train_split"
    included_substrings:
      - "v3_sc0_staging"
      - "v3_sc1_staging"
      - "v3_sc2_staging"
    excluded_substrings: []
  -
    name: "scene_val_split"
    included_substrings:
      - "v3_sc3_staging"
    excluded_substrings: []
  -
    name: "scene_test_split"
    included_substrings:
      - "v3_sc4_staging"
    excluded_substrings: []

object_sets:
  -
    name: "hab2"
    included_substrings:
      - "002_master_chef_can"
      - "003_cracker_box"
      - "004_sugar_box"
      - "005_tomato_soup_can"
      - "007_tuna_fish_can"
      - "008_pudding_box"
      - "009_gelatin_box"
      - "010_potted_meat_can"
      - "024_bowl"
    excluded_substrings: []
  -
    name: "fruits"
    included_substrings:
      - "013_apple"
    excluded_substrings: []
  -
    name: "basket"
    included_substrings:
      - "024_bowl"
    excluded_substrings: []
receptacle_sets:
  -
    name: "drawer"
    included_object_substrings:
      - ""
    excluded_object_substrings: []
    included_receptacle_substrings:
      - "receptacle_aabb_drawer_right_top_frl_apartment_kitchen_counter"
    excluded_receptacle_substrings: []
  -
    name: "table"
    included_object_substrings:
      - ""
    excluded_object_substrings: []
    included_receptacle_substrings:
      - "receptacle_aabb_Tbl1_Top1_frl_apartment_table_01"
    excluded_receptacle_substrings: []

  -
    name: "fridge"
    included_object_substrings:
      - ""
    excluded_object_substrings: []
    included_receptacle_substrings:
      - "receptacle_aabb_middle_topfrl_apartment_refrigerator"
    excluded_receptacle_substrings: []

  -
    name: "clutter"
    included_object_substrings:
      - ""
    excluded_object_substrings: []
    included_receptacle_substrings:
      - "receptacle_aabb_Chr1_Top1_frl_apartment_chair_01"
      - "receptacle_aabb_Tbl1_Top1_frl_apartment_table_01"
      - "receptacle_aabb_Tbl2_Top1_frl_apartment_table_02"
      - "receptacle_aabb_TvStnd1_Top1_frl_apartment_tvstand"
      - "receptacle_aabb_sink_kitchen_counter"
      - "receptacle_aabb_counter_right_kitchen_counter"
      - "receptacle_aabb_counter_left_kitchen_counter"
      - "receptacle_aabb_Sofa_frl_apartment_sofa"

      # Inside of articulated objects
      - "receptacle_aabb_middle_topfrl_apartment_refrigerator"
    excluded_receptacle_substrings: []

scene_sampler:
  type: "subset"
  params:
    scene_sets: ["scene_train_split", "scene_val_split", "scene_test_split"]

object_samplers:
  -
    name: "clutter"
    type: "uniform"
    params:
      object_sets: ["hab2"]
      receptacle_sets: ["clutter"]
      num_samples: [27, 27]
      orientation_sampling: "up"
  -
    name: "basket"
    type: "uniform"
    params:
      object_sets: ["basket"]
      receptacle_sets: ["drawer"]
      num_samples: [1, 1]
      orientation_sampling: "up"
      nav_to_min_distance: 1.5
  -
    name: "fruit"
    type: "uniform"
    params:
      object_sets: ["fruits"]
      receptacle_sets: ["fridge"]
      num_samples: [1, 1]
      orientation_sampling: "up"
      sample_region_ratio:
        "receptacle_aabb_middle_topfrl_apartment_refrigerator": 0.5
      nav_to_min_distance: 1.5

object_target_samplers:
  -
    name: "bowl_target"
    type: "uniform"
    params:
      object_samplers: ["basket"]
      receptacle_sets: ["table"]
      num_samples: [1, 1]
      orientation_sampling: "up"
      nav_to_min_distance: 1.5
  -
    name: "fruit_target"
    type: "uniform"
    params:
      object_samplers: ["fruit"]
      receptacle_sets: ["table"]
      num_samples: [1, 1]
      orientation_sampling: "up"
      nav_to_min_distance: 1.5

markers:
  - name: "cab_push_point_7"
    type: "articulated_object"
    params:
      offset: [0.3,0.0,0]
      link: "drawer1_top"
      object: "kitchen_counter_:0000"
  - name: "cab_push_point_6"
    type: "articulated_object"
    params:
      offset: [0.3,0.0,0]
      link: "drawer2_top"
      object: "kitchen_counter_:0000"
  - name: "cab_push_point_5"
    type: "articulated_object"
    params:
      offset: [0.3,0.0,0]
      link: "drawer3"
      object: "kitchen_counter_:0000"
  - name: "cab_push_point_4"
    type: "articulated_object"
    params:
      offset: [0.3,0.0,0]
      link: "drawer4"
      object: "kitchen_counter_:0000"
  - name: "fridge_push_point"
    type: "articulated_object"
    params:
      offset: [0.10,-0.62,0.2]
      link: "top_door"
      object: "fridge_:0000"
