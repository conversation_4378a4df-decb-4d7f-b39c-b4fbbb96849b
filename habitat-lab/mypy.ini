[mypy]
disable_error_code=override

# do not follow imports (except for ones found in typeshed)
ignore_missing_imports = True
#Ignore errors for third parties
ignore_errors = False
follow_imports = silent

# treat Optional per PEP 484
strict_optional = False

warn_unused_configs = True
warn_redundant_casts = True
# ensure all execution paths are returning
warn_no_return= True
warn_unreachable = True
allow_redefinition = True

show_error_codes = True
check_untyped_defs = True


files=
    habitat-lab/habitat,
    habitat-baselines/habitat_baselines,
    test
python_version = 3.9

# Third Party Dependencies

[mypy-test.*]
ignore_errors = False

[mypy-habitat-lab.*]
ignore_errors = False

[mypy-habitat_sim.*]
ignore_errors = False

[mypy-habitat-baselines.*]
ignore_errors = False
