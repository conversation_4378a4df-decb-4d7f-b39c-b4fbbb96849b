[aliases]
test=pytest

[flake8]
select = A,B,C,F,R,W,SIM
exclude =
	.git,
	__pycache__,
	build,
	data,
	dist,
	docs,
	src/deps
max-line-length = 88
# A003 prevents class attrs from having builtin name properties
# C401, and C402 are ignored to make scanning between dict and set easy
# C408 ignored because we like the dict keyword argument syntax
# R504 has some false positives since it doesn't care about side effects
# W503 is incompatible with Black
# SIM105 is a nice suggestion but except: ImportError is also really readable
# SIM106 has too many false positives
# SIM113 has too many false positives
ignore =
	A003,
	C401,C402,C408,
	SIM105,SIM106,SIM113,
	R504,
	W503,
per-file-ignores =
	*/__init__.py:F401
	examples/tutorials/nb_python/*.py:B008,F841

[tool:pytest]
addopts = --verbose -rsxX -q
testpaths = test
