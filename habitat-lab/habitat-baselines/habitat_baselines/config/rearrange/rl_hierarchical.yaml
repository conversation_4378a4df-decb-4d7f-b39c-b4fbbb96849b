# @package _global_

# Config for running hierarchical policies where a high-level (HL) policy selects from a set of low-level (LL) policies.
# Supports different HL policy configurations and using a variety of LL policies.

defaults:
  - /benchmark/rearrange: rearrange_easy
  - /habitat_baselines: habitat_baselines_rl_config_base
  - /habitat/simulator/sim_sensors@habitat_baselines.eval.extra_sim_sensors.third_rgb_sensor: third_rgb_sensor
  - /habitat_baselines/rl/policy/obs_transforms:
    - add_virtual_keys_base
  - /habitat_baselines/rl/policy: hl_fixed
  - /habitat_baselines/rl/policy/hierarchical_policy/defined_skills: nn_skills
  - /habitat/task/measurements:
    - composite_subgoal_reward
  - _self_

habitat:
  task:
    measurements:
      composite_subgoal_reward:
        stage_sparse_reward: 1.0
    reward_measure: composite_subgoal_reward
    slack_reward: -0.01
    success_reward: 5.0
habitat_baselines:
  verbose: False
  trainer_name: "ddppo"
  updater_name: "HRLPP<PERSON>"
  distrib_updater_name: "HRLDDP<PERSON>"
  torch_gpu_id: 0
  video_fps: 30
  eval_ckpt_path_dir: ""
  num_environments: 4
  writer_type: 'tb'
  num_updates: -1
  total_num_steps: 5.0e7
  log_interval: 10
  num_checkpoints: 10
  force_torch_single_threaded: True
  eval_keys_to_include_in_name: ['composite_success']
  load_resume_state_config: False
  rollout_storage_name: "HrlRolloutStorage"

  eval:
    use_ckpt_config: False
    should_load_ckpt: False
    video_option: ["disk"]

  rl:
    ppo:
      # ppo params
      clip_param: 0.2
      ppo_epoch: 1
      num_mini_batch: 2
      value_loss_coef: 0.5
      entropy_coef: 0.0001
      lr: 2.5e-4
      eps: 1e-5
      max_grad_norm: 0.2
      num_steps: 128
      use_gae: True
      gamma: 0.99
      tau: 0.95

    ddppo:
      sync_frac: 0.6
      # The PyTorch distributed backend to use
      distrib_backend: NCCL
      # Visual encoder backbone
      pretrained_weights: data/ddppo-models/gibson-2plus-resnet50.pth
      # Initialize with pretrained weights
      pretrained: False
      # Initialize just the visual encoder backbone with pretrained weights
      pretrained_encoder: False
      # Whether the visual encoder backbone will be trained.
      train_encoder: True
      # Whether to reset the critic linear layer
      reset_critic: False
      # Model parameters
      backbone: resnet18
      rnn_type: LSTM
      num_recurrent_layers: 2
