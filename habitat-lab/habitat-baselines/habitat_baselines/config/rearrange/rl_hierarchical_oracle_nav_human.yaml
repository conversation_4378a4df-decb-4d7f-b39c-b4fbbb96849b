# @package _global_

# Extends the `rl_hierarchical_oracle_nav` config to use the human avatar.
# Several things change to support the oracle navigation:
# - Add ignore_grip parameter
# - Add sliding.
# - Use the oracle navigation skill.

defaults:
  - rl_hierarchical_oracle_nav
  - /habitat/task/lab_sensors:
    - relative_resting_pos_sensor
    - target_start_sensor
    - goal_sensor
    - humanoid_joint_sensor
    - is_holding_sensor
    - end_effector_sensor
    - target_start_gps_compass_sensor
    - target_goal_gps_compass_sensor
    - localization_sensor
  - override /habitat/task/actions:
    - base_velocity
    - rearrange_stop
    - oracle_nav_action
  - override /habitat/task/rearrange: rearrange_easy_base
  - override /habitat_baselines/rl/policy/hierarchical_policy/defined_skills: oracle_skills
  - _self_

habitat:
  task:
    actions:
      oracle_nav_action:
        motion_control: human_joints
        spawn_max_dist_to_obj: 1.7
  gym:
    obs_keys:
      - head_depth
      - relative_resting_position
      - obj_start_sensor
      - obj_goal_sensor
      - obj_start_gps_compass
      - obj_goal_gps_compass
      - humanoid_joint_sensor
      - localization_sensor
      - is_holding
  simulator:
    agents:
      main_agent:
        articulated_agent_urdf: 'data/humanoids/humanoid_data/amass_male.urdf'
        articulated_agent_type: KinematicHumanoid
        motion_data_path: "data/humanoids/humanoid_data/walking_motion_processed.pkl"

habitat_baselines:
  rl:
    policy:
      hierarchical_policy:
        defined_skills:
          nav_to_obj:
            ignore_grip: True
            pddl_action_names: ["nav", "nav_to_receptacle_by_name"]
          open_cab:
            skill_name: "NoopSkillPolicy"
            max_skill_steps: 1
            apply_postconds: True
            force_end_on_timeout: False
            pddl_action_names: ["open_cab_by_name"]
            ignore_grip: True
          open_fridge:
            skill_name: "NoopSkillPolicy"
            max_skill_steps: 1
            apply_postconds: True
            force_end_on_timeout: False
            pddl_action_names: ["open_fridge_by_name"]
            ignore_grip: True
          close_cab:
            skill_name: "NoopSkillPolicy"
            obs_skill_inputs: ["obj_start_sensor"]
            max_skill_steps: 1
            apply_postconds: True
            force_end_on_timeout: False
            pddl_action_names: ["close_cab_by_name"]
            ignore_grip: True
          close_fridge:
            skill_name: "NoopSkillPolicy"
            obs_skill_inputs: ["obj_start_sensor"]
            max_skill_steps: 1
            apply_postconds: True
            force_end_on_timeout: False
            pddl_action_names: ["close_fridge_by_name"]
            ignore_grip: True
          pick:
            skill_name: "NoopSkillPolicy"
            obs_skill_inputs: ["obj_start_sensor"]
            max_skill_steps: 1
            apply_postconds: True
            force_end_on_timeout: False
            ignore_grip: True
          place:
            skill_name: "NoopSkillPolicy"
            obs_skill_inputs: ["obj_goal_sensor"]
            max_skill_steps: 1
            apply_postconds: True
            force_end_on_timeout: False
            ignore_grip: True
          wait:
            skill_name: "WaitSkillPolicy"
            ignore_grip: True
          reset_arm:
            skill_name: "NoopSkillPolicy"
            max_skill_steps: 1
            ignore_grip: True
            force_end_on_timeout: False
