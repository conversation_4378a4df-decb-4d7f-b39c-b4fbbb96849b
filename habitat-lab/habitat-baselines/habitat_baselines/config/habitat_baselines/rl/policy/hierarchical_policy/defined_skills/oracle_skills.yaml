# Oracle skills that will teleport to the skill post-condition. When automatically setting predicates you may want to run the simulation in kinematic mode:
# To run in kinematic mode, add: `habitat.simulator.kinematic_mode=True habitat.simulator.ac_freq_ratio=1 habitat.task.measurements.force_terminate.max_accum_force=-1.0 habitat.task.measurements.force_terminate.max_instant_force=-1.0`

defaults:
  - /habitat/task/actions:
    - pddl_apply_action

open_cab:
  skill_name: "NoopSkillPolicy"
  max_skill_steps: 1
  apply_postconds: True
  force_end_on_timeout: False
  pddl_action_names: ["open_cab_by_name"]

open_fridge:
  skill_name: "NoopSkillPolicy"
  max_skill_steps: 1
  apply_postconds: True
  force_end_on_timeout: False
  pddl_action_names: ["open_fridge_by_name"]

close_cab:
  skill_name: "NoopSkillPolicy"
  obs_skill_inputs: ["obj_start_sensor"]
  max_skill_steps: 1
  apply_postconds: True
  force_end_on_timeout: False
  pddl_action_names: ["close_cab_by_name"]

close_fridge:
  skill_name: "NoopSkillPolicy"
  obs_skill_inputs: ["obj_start_sensor"]
  max_skill_steps: 1
  apply_postconds: True
  force_end_on_timeout: False
  pddl_action_names: ["close_fridge_by_name"]

pick:
  skill_name: "NoopSkillPolicy"
  obs_skill_inputs: ["obj_start_sensor"]
  max_skill_steps: 1
  apply_postconds: True
  force_end_on_timeout: False

place:
  skill_name: "NoopSkillPolicy"
  obs_skill_inputs: ["obj_goal_sensor"]
  max_skill_steps: 1
  apply_postconds: True
  force_end_on_timeout: False

wait:
  skill_name: "WaitSkillPolicy"
  max_skill_steps: -1

nav_to_obj:
  skill_name: "NoopSkillPolicy"
  obs_skill_inputs: ["goal_to_agent_gps_compass"]
  max_skill_steps: 1
  apply_postconds: True
  force_end_on_timeout: False
  obs_skill_input_dim: 2
  pddl_action_names: ["nav", "nav_to_receptacle_by_name"]

reset_arm:
  skill_name: "ResetArmSkill"
  max_skill_steps: 50
  reset_joint_state: [-4.50e-01, -1.07e00, 9.95e-02, 9.38e-01, -7.88e-04, 1.57e00, 4.62e-03]
  force_end_on_timeout: False
