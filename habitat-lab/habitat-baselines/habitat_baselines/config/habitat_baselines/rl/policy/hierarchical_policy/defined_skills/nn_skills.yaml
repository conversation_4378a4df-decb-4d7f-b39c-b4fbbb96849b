open_cab:
  skill_name: "ArtObjSkillPolicy"
  load_ckpt_file: "data/models/open_cab.pth"

open_fridge:
  skill_name: "ArtObjSkillPolicy"
  load_ckpt_file: "data/models/open_fridge.pth"

close_cab:
  skill_name: "ArtObjSkillPolicy"
  load_ckpt_file: "data/models/close_cab.pth"

close_fridge:
  skill_name: "ArtObjSkillPolicy"
  load_ckpt_file: "data/models/close_fridge.pth"

pick:
  skill_name: "PickSkillPolicy"
  obs_skill_inputs: ["obj_start_sensor"]
  load_ckpt_file: "data/models/pick.pth"

place:
  skill_name: "PlaceSkillPolicy"
  obs_skill_inputs: ["obj_goal_sensor"]
  load_ckpt_file: "data/models/place.pth"

wait:
  skill_name: "WaitSkillPolicy"
  max_skill_steps: -1
  force_end_on_timeout: False

nav_to_obj:
  skill_name: "NavSkillPolicy"
  obs_skill_inputs: ["goal_to_agent_gps_compass"]
  load_ckpt_file: "data/models/nav.pth"
  max_skill_steps: 300
  obs_skill_input_dim: 2
  pddl_action_names: ["nav", "nav_to_receptacle"]

reset_arm:
  skill_name: "ResetArmSkill"
  max_skill_steps: 50
  reset_joint_state: [-4.50e-01, -1.08e00, 9.95e-02, 9.38e-01, -7.88e-04, 1.57e00, 4.62e-03]
  force_end_on_timeout: False
