name: "HierarchicalPolicy"
obs_transforms:
  add_virtual_keys:
    virtual_keys:
      "goal_to_agent_gps_compass": 2
hierarchical_policy:
  high_level_policy:
    name: "NeuralHighLevelPolicy"
    allowed_actions:
      - nav
      - pick
      - place
      - nav_to_receptacle_by_name
      - open_fridge_by_name
      - close_fridge_by_name
      - open_cab_by_name
      - close_cab_by_name
    allow_other_place: False
    hidden_dim: 512
    use_rnn: True
    rnn_type: 'LSTM'
    backbone: resnet18
    normalize_visual_inputs: False
    num_rnn_layers: 2
    policy_input_keys:
      - "head_depth"
  defined_skills: {}
