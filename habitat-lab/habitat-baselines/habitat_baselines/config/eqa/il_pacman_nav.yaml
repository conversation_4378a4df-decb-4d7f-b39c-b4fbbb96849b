# @package _global_

defaults:
  - /habitat_baselines: habitat_baselines_il_config_base
  - /benchmark/nav: eqa_rgbonly_mp3d
  - override /habitat/simulator/<EMAIL>.main_agent: rgbd_agent
  - _self_

habitat:
  simulator:
    agents:
      main_agent:
        sim_sensors:
          rgb_sensor:
            width: 256
            height: 256
          depth_sensor:
            width: 256
            height: 256

habitat_baselines:
  trainer_name: "pacman"
  torch_gpu_id: 0
  eval_ckpt_path_dir: "data/eqa/nav/checkpoints/"
  num_processes: 1
  checkpoint_folder: "data/eqa/nav/checkpoints"
  tensorboard_dir: "data/eqa/nav/tb"
  log_interval: 10
  checkpoint_interval: 1
  # Force PyTorch to be single threaded as
  # this improves performance considerably
  force_torch_single_threaded: True
  il:
    only_vqa_task: False # if True, only last `num_frames` will be saved to disk.
    #if False, all frames for each episode are saved to disk (for NAV task later)
    frame_dataset_path: "data/datasets/eqa/frame_dataset/{split}"
    eqa_cnn_pretrain_ckpt_path: "data/eqa/eqa_cnn_pretrain/checkpoints/epoch_5.ckpt"
    results_dir: "data/eqa/nav/results/{split}"
    log_metrics: True
    output_log_dir: data/eqa/nav/logs
    eval_save_results: True
    eval_save_results_interval: 10
    nav:
      # nav params
      max_epochs: 20
      batch_size: 20
      lr: 1e-3
      max_controller_actions: 5
      max_episode_length: 100
