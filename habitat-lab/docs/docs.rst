..
    <PERSON><PERSON> defined here gets set globally for everything else:

    -   use :py:`code` for inline code with highlighted Python syntax
..

.. role:: py(code)
    :language: py

.. due to current limitations in m.css, all underscored members have to be
    listed here in order to be visible, it's not enough to list them in a class
    / module docstring. All underscored members are otherwise treated as
    private and not exposed in the docs

.. py:data:: habitat.core.embodied_task.Measure._metric
