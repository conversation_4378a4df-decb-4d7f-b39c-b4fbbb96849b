Habitat Lab Documentation
#########################

A modular high-level library to train embodied AI agents across a variety of
tasks, environments, and simulators.

`Tutorials`_
============

.. class:: m-table m-fullwidth

=================================================== ========================================================================================================================================================== ======================
Quickstart                                          :ref:`Page <std:doc:quickstart>`

Habitat Lab Demo                                    :ref:`Page <std:doc:habitat-lab-demo>`

Habitat Lab TopdownMap Visualization                :ref:`Page <std:doc:habitat-lab-tdmap-viz>`                                                                                                                `Interactive Colab <https://colab.research.google.com/github/facebookresearch/habitat-lab/blob/main/examples/tutorials/colabs/Habitat_Lab_TopdownMap_Visualization.ipynb>`__

Habitat 2.0 Overview                                :ref:`Page <std:doc:habitat2>`                                                                                                                             `Interactive Colab 1 <https://colab.research.google.com/github/facebookresearch/habitat-lab/blob/main/examples/tutorials/colabs/Habitat2_Quickstart.ipynb>`__, `Interactive Colab 2 <https://colab.research.google.com/github/facebookresearch/habitat-lab/blob/main/examples/tutorials/colabs/habitat2_gym_tutorial.ipynb>`__

View, Transform and Warp                            :ref:`Page <std:doc:view-transform-warp>`
=================================================== ========================================================================================================================================================== ======================

`Package reference`_
====================

-   :ref:`habitat.core.env`
-   :ref:`habitat.core.embodied_task`
-   :ref:`habitat.core.dataset`
-   :ref:`habitat.core.simulator`
-   :ref:`habitat.core.vector_env`
-   :ref:`habitat.Agent`
-   :ref:`habitat.Benchmark`
