# @package _global_
defaults:
  - /benchmark/nav/eqa_mp3d.yaml
  - _self_

habitat:
  environment:
    max_episode_steps: 1000
    iterator_options:
      shuffle: False

  simulator:
    scene: data/scene_datasets/mp3d/17DRP5sb8fy/17DRP5sb8fy.glb
    forward_step_size: 0.1
    turn_angle: 9
    agents:
      main_agent:
        sim_sensors:
          rgb_sensor:
            height: 512
            width: 512
            hfov: 45
            position: [0, 1.09, 0]
            type: HabitatSimRGBSensor

  dataset:
    type: MP3DEQA-v1
    split: val
    data_path: "data/datasets/eqa/mp3d/v1/{split}/{split}.json.gz"
    scenes_dir: "data/scene_datasets/"
