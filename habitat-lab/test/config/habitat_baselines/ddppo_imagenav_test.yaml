# @package _global_

defaults:
  - /benchmark/nav/imagenav: imagenav_test
  - /habitat_baselines: habitat_baselines_rl_config_base
  - _self_

habitat_baselines:
  trainer_name: "ddppo"
  torch_gpu_id: 0
  tensorboard_dir: ""
  video_dir: ""
  eval_ckpt_path_dir: "data/test_checkpoints/ddppo/imagenav/ckpt.0.pth"
  num_environments: 1
  checkpoint_folder: "data/test_checkpoints/ddppo/imagenav/"
  num_updates: 2
  log_interval: 100
  num_checkpoints: 2
  test_episode_count: 2
  # Force PyTorch to be single threaded as
  # this improves performance considerably
  force_torch_single_threaded: True


  rl:
    policy:
      name: "PointNavResNetPolicy"

    ppo:
      # ppo params
      clip_param: 0.2
      ppo_epoch: 2
      num_mini_batch: 1
      value_loss_coef: 0.5
      entropy_coef: 0.01
      lr: 2.5e-4
      eps: 1e-5
      max_grad_norm: 0.2
      num_steps: 16
      use_gae: True
      gamma: 0.99
      tau: 0.95
      use_linear_clip_decay: False
      use_linear_lr_decay: False
      reward_window_size: 50

      use_normalized_advantage: False

      hidden_size: 512

    ddppo:
      sync_frac: 0.6
      # The PyTorch distributed backend to use
      distrib_backend: GLOO
      # Visual encoder backbone
      pretrained_weights: data/ddppo-models/gibson-2plus-resnet50.pth
      # Initialize with pretrained weights
      pretrained: False
      # Initialize just the visual encoder backbone with pretrained weights
      pretrained_encoder: False
      # Whether the visual encoder backbone will be trained.
      train_encoder: True
      # Whether to reset the critic linear layer
      reset_critic: True

      # Model parameters
      backbone: resnet18
      rnn_type: LSTM
      num_recurrent_layers: 2

      force_distributed: True
