# @package _global_

defaults:
  - /benchmark/nav/instance_imagenav: instance_imagenav_hm3d_v2
  - /habitat_baselines: habitat_baselines_rl_config_base
  - /habitat_baselines/rl/policy/obs_transforms:
    - center_cropper_base
    - resize_shortest_edge_base
  - _self_

habitat:
  dataset:
    split: minival
  task:
    success_reward: 2.5
    slack_reward: -1e-3

habitat_baselines:
  torch_gpu_id: 0
  tensorboard_dir: ""
  video_dir: ""
  test_episode_count: 2
  eval_ckpt_path_dir: data/test_checkpoints/ddppo/instance_imagenav/ckpt.0.pth
  num_environments: 1
  checkpoint_folder: data/test_checkpoints/ddppo/instance_imagenav
  trainer_name: ddppo
  num_updates: 2
  log_interval: 10
  num_checkpoints: 2
  # Force PyTorch to be single threaded as
  # this improves performance considerably
  force_torch_single_threaded: True

  rl:
    policy:
      name: PointNavResNetPolicy
      obs_transforms:
        resize_shortest_edge:
          trans_keys: [rgb, depth, semantic, instance_imagegoal]

    ppo:
      # ppo params
      clip_param: 0.2
      ppo_epoch: 2
      num_mini_batch: 1
      value_loss_coef: 0.5
      entropy_coef: 0.01
      lr: 2.5e-4
      eps: 1e-5
      max_grad_norm: 0.2
      num_steps: 16
      use_gae: True
      gamma: 0.99
      tau: 0.95
      use_linear_clip_decay: False
      use_linear_lr_decay: False
      reward_window_size: 50

      use_normalized_advantage: False

      hidden_size: 512

    ddppo:
      sync_frac: 0.6
      # The PyTorch distributed backend to use
      distrib_backend: GLOO
      # Visual encoder backbone
      pretrained_weights: data/ddppo-models/gibson-2plus-resnet50.pth
      # Initialize with pretrained weights
      pretrained: False
      # Initialize just the visual encoder backbone with pretrained weights
      pretrained_encoder: False
      # Whether the visual encoder backbone will be trained.
      train_encoder: True
      # Whether to reset the critic linear layer
      reset_critic: True

      # Model parameters
      backbone: resnet18
      rnn_type: LSTM
      num_recurrent_layers: 2

      force_distributed: True
