#!/usr/bin/env python3

from typing import Any, Optional, List, Union
import base64
import io
import json

import numpy as np
import requests
from PIL import Image


class DAMClient:
    """Describe Anything Model (DAM) Client for sending requests to DAM server."""

    def __init__(self, server_url: str = "http://localhost:9092", model_name: str = "describe_anything_model"):
        """
        Initialize DAM client.
        
        Args:
            server_url (str): The URL of the DAM server (default: http://localhost:9092)
            model_name (str): The model name for API requests (default: describe_anything_model)
        """
        self.server_url = server_url.rstrip('/')
        self.model_name = model_name
        
    def _combine_image_and_mask(self, image: Union[np.ndarray, Image.Image], mask: np.ndarray) -> str:
        """
        Combine image and mask into RGBA format expected by DAM server.
        
        Args:
            image: Input image (RGB)
            mask: Binary mask where 1 indicates region of interest
            
        Returns:
            str: Base64 encoded RGBA image with mask in alpha channel
        """
        if isinstance(image, np.ndarray):
            pil_image = Image.fromarray(image)
        else:
            pil_image = image
            
        # Convert to RGB if needed
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')
            
        # Ensure mask is binary and same size as image
        if mask.dtype != np.uint8:
            mask = (mask > 0).astype(np.uint8) * 255
        else:
            mask = (mask > 0).astype(np.uint8) * 255
            
        # Resize mask to match image if needed
        if mask.shape[:2] != pil_image.size[::-1]:
            mask_pil = Image.fromarray(mask, mode='L')
            mask_pil = mask_pil.resize(pil_image.size, Image.NEAREST)
            mask = np.array(mask_pil)
        
        # Convert image to numpy array
        image_np = np.array(pil_image)
        
        # Create RGBA image
        rgba_image = np.zeros((image_np.shape[0], image_np.shape[1], 4), dtype=np.uint8)
        rgba_image[:, :, :3] = image_np  # RGB channels
        rgba_image[:, :, 3] = mask       # Alpha channel with mask
        
        # Convert back to PIL and encode
        rgba_pil = Image.fromarray(rgba_image, 'RGBA')
        buffer = io.BytesIO()
        rgba_pil.save(buffer, format="PNG")
        image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        return f"data:image/png;base64,{image_base64}"
    
    def describe_with_mask(self, image: Union[np.ndarray, Image.Image], mask: np.ndarray, 
                          prompt: str = "Describe this image region in detail.") -> str:
        """
        Get description for a specific region of the image using a mask.
        
        Args:
            image (numpy.ndarray or PIL.Image): The input image
            mask (numpy.ndarray): Binary mask indicating the region of interest
            prompt (str): The prompt for description (default: "Describe this image region in detail.")
            
        Returns:
            str: The description text for the specified region
        """
        print(f"DAMClient.describe_with_mask: image shape {image.shape if isinstance(image, np.ndarray) else image.size}, mask shape {mask.shape}")
        
        # Combine image and mask into RGBA format
        rgba_image_base64 = self._combine_image_and_mask(image, mask)
        
        # Prepare the request payload in OpenAI format
        payload = {
            "model": self.model_name,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": rgba_image_base64
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 512,
            "temperature": 0.2
        }
        
        try:
            response = requests.post(
                f"{self.server_url}/chat/completions",
                headers={"Content-Type": "application/json"},
                json=payload,
                timeout=60
            )
            response.raise_for_status()
            
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                raise ValueError(f"Unexpected response format: {result}")
                
        except requests.exceptions.RequestException as e:
            raise RuntimeError(f"Failed to get description from DAM server: {e}")
    
    def describe_full_image(self, image: Union[np.ndarray, Image.Image], 
                           prompt: str = "Describe this image in detail.") -> str:
        """
        Get description for the full image.
        
        Args:
            image (numpy.ndarray or PIL.Image): The input image
            prompt (str): The prompt for description
            
        Returns:
            str: The description text for the full image
        """
        print(f"DAMClient.describe_full_image: image shape {image.shape if isinstance(image, np.ndarray) else image.size}")
        
        # Create a full mask (all ones) for the entire image
        if isinstance(image, np.ndarray):
            h, w = image.shape[:2]
        else:
            w, h = image.size
        
        full_mask = np.ones((h, w), dtype=np.uint8) * 255
        
        # Use the same logic as describe_with_mask
        return self.describe_with_mask(image, full_mask, prompt)
    
    def check_server_status(self) -> bool:
        """
        Check if the DAM server is running and accessible.
        
        Returns:
            bool: True if server is accessible, False otherwise
        """
        try:
            # Create a simple test image
            test_image = np.ones((100, 100, 3), dtype=np.uint8) * 128  # Gray image
            test_mask = np.ones((100, 100), dtype=np.uint8) * 255      # Full mask
            
            rgba_image_base64 = self._combine_image_and_mask(test_image, test_mask)
            
            test_payload = {
                "model": self.model_name,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "test"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": rgba_image_base64
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 1
            }
            response = requests.post(
                f"{self.server_url}/chat/completions",
                headers={"Content-Type": "application/json"},
                json=test_payload,
                timeout=10
            )
            # Even if the request fails due to model processing, 
            # a 200 or 500 status means the server is running and accessible
            return response.status_code in [200, 500]
        except requests.exceptions.RequestException:
            return False


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="DAM Client Test")
    parser.add_argument("--server_url", type=str, default="http://localhost:9092", 
                       help="DAM server URL")
    parser.add_argument("--model_name", type=str, default="describe_anything_model",
                       help="Model name for API requests")
    parser.add_argument("--image_path", type=str, required=True,
                       help="Path to test image")
    parser.add_argument("--mask_path", type=str, default=None,
                       help="Path to mask image (binary mask)")
    
    args = parser.parse_args()
    
    # Initialize client
    client = DAMClient(args.server_url, args.model_name)
    
    print(f"Testing DAM server at {args.server_url}...")
    
    # Check server status
    if not client.check_server_status():
        print(f"Error: DAM server at {args.server_url} is not accessible!")
        exit(1)
    
    print(f"✓ DAM server at {args.server_url} is accessible!")
    
    # Load test image
    try:
        image = Image.open(args.image_path)
        image_np = np.array(image)
        print(f"✓ Loaded image: {image.size}")
    except Exception as e:
        print(f"Error loading image: {e}")
        exit(1)
    
    # Test description methods
    try:
        if args.mask_path:
            # Load mask and test masked description
            try:
                mask_image = Image.open(args.mask_path).convert('L')
                mask_np = np.array(mask_image)
                print(f"✓ Loaded mask: {mask_image.size}")
                description = client.describe_with_mask(image_np, mask_np)
                print("\n--- Description with mask ---")
                print(description)
            except Exception as e:
                print(f"Error loading mask: {e}")
                exit(1)
        else:
            # Test full image description
            description = client.describe_full_image(image_np)
            print("\n--- Full image description ---")
            print(description)
            
    except Exception as e:
        print(f"Error getting description: {e}")
        import traceback
        traceback.print_exc()
