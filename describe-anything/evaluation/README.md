# Evaluating models on DLC-Bench

## Installation

### Install `dam` package to run inference of describe anything

This is the same as the general installation of `dam` in the [README](../README.md).

### Install vLLM

We use `vllm` to serve Llama 3.1 to 8B to evaluate the model outputs. Please install `vllm` suitable for your CUDA version. The following is the command for CUDA 11.8 and Python 3.10.
```
export VLLM_VERSION=0.5.3.post1
export PYTHON_VERSION=310
pip install https://github.com/vllm-project/vllm/releases/download/v${VLLM_VERSION}/vllm-${VLLM_VERSION}+cu118-cp${PYTHON_VERSION}-cp${PYTHON_VERSION}-manylinux1_x86_64.whl --extra-index-url https://download.pytorch.org/whl/cu118
```

## Prepare evaluation data

Please download the DLC-Bench dataset from [huggingface datasets](https://huggingface.co/datasets/nvidia/DLC-Bench) and put the `DLC-bench` folder in this directory:

```
git lfs install
git clone https://huggingface.co/datasets/nvidia/DLC-Bench
```

## Run evaluation

### Start a vLLM backend

This does not have to be on the same GPU as the one running the model to get outputs. The vLLM backend is used to evaluate the generated model outputs. If you would like to run vLLM and run the model inference (`get_model_outputs.py`) on the same GPU, you might want to reduce the GPU memory usage by setting `--gpu-memory-utilization 0.5` so that you have enough memory for model inference. The following command starts a vLLM backend on GPU 1:

```sh
export LLM_NAME=meta-llama/Meta-Llama-3.1-8B-Instruct
CUDA_VISIBLE_DEVICES=1 vllm serve $LLM_NAME --tensor-parallel-size 1 --port 9000 --max-model-len 8192
```

### Obtain model outputs

Download the model checkpoint in `../checkpoints/` and use the following script to run describe anything model inference and cache the outputs in `get_model_outputs.py`. The model outputs are cached in `model_outputs_cache/`.

```sh
python get_model_outputs.py --model_type dam --model_path nvidia/DAM-3B
```

Note that this model (`nvidia/DAM-3B`) comes with prompt augmentation in training to enhance prompt following, but the performance on the benchmark is slightly worse than the model without prompt augmentation reported in the main Table.

### Evaluate model outputs

Use the following script to evaluate the model outputs.

```sh
python eval_model_outputs.py --pred model_outputs_cache/dam_3b_v1.json --base-url "http://localhost:9000/v1"
```

Reference cache generated by `get_model_outputs.py` and `eval_model_outputs.py` are stored in `model_outputs_cache/`. You could remove the cache files if you want to re-run the evaluation.

Reference results:

```
Summary (Pos    Neg     Avg(Pos, Neg)): 0.510,  0.830,  0.670
```

## Evaluating your own model

If you would like to evaluate your own model outputs, you could reference the cache format with files in `model_outputs_cache/` and use `eval_model_outputs.py` to evaluate your model outputs.
