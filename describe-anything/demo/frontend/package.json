{"name": "segment-anything-mini-demo", "version": "0.1.0", "license": "MIT", "scripts": {"build": "yarn run clean-dist && webpack --config=configs/webpack/prod.js && mv dist/*.wasm dist/js && rsync -r --delete dist ../", "clean-dist": "rimraf dist/*", "lint": "eslint './src/**/*.{js,ts,tsx}' --quiet", "start": "yarn run start-dev", "test": "yarn run start-model-test", "start-dev": "webpack serve --config=configs/webpack/dev.js"}, "devDependencies": {"@babel/core": "^7.18.13", "@babel/preset-env": "^7.18.10", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.7", "@testing-library/react": "^13.3.0", "@types/node": "^18.7.13", "@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "@types/underscore": "^1.11.4", "@typescript-eslint/eslint-plugin": "^5.35.1", "@typescript-eslint/parser": "^5.35.1", "babel-loader": "^8.2.5", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.7.1", "dotenv": "^16.0.2", "dotenv-webpack": "^8.0.1", "eslint": "^8.22.0", "eslint-plugin-react": "^7.31.0", "file-loader": "^6.2.0", "fork-ts-checker-webpack-plugin": "^7.2.13", "friendly-errors-webpack-plugin": "^1.7.0", "html-webpack-plugin": "^5.5.0", "image-webpack-loader": "^8.1.0", "postcss-loader": "^7.0.1", "postcss-preset-env": "^7.8.0", "process": "^0.11.10", "rimraf": "^3.0.2", "sass": "^1.54.5", "sass-loader": "^13.0.2", "style-loader": "^3.3.1", "tailwindcss": "^3.1.8", "ts-loader": "^9.3.1", "typescript": "^4.8.2", "webpack": "^5.74.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.10.0", "webpack-dotenv-plugin": "^2.1.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@gradio/client": "^1.7.1", "npyjs": "^0.4.0", "onnxruntime-web": "1.14.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-refresh": "^0.14.0", "underscore": "^1.13.6", "axios": "^1.6.7"}}