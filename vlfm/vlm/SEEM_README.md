# SEEM (Segment Everything Everywhere All at Once) Implementation

This directory contains a Python implementation of SEEM, a powerful multi-modal segmentation model that supports various types of prompts for image segmentation.

## Features

SEEM supports the following prompt types:

### 🎯 Visual Prompts
- **Points**: Click-based segmentation with positive/negative points
- **Bounding Boxes**: Rectangle-based object segmentation  
- **Scribbles**: Free-form drawing for region specification
- **Masks**: Binary mask-based prompts

### 📝 Text Prompts
- **Natural Language**: Describe objects in plain English
- **Grounding Segmentation**: Segment objects based on text descriptions
- **Semantic Labels**: Get category labels for segmented objects

### 🖼️ Image Prompts
- **Reference Images**: Segment similar objects using example images
- **Cross-Image Matching**: Find objects with similar appearance/semantics

### 🎵 Audio Prompts
- **Speech-to-Text**: Convert audio descriptions to text prompts
- **Whisper Integration**: Automatic speech recognition for segmentation

### 🔄 Advanced Features
- **Combined Prompts**: Use multiple prompt types simultaneously
- **Interactive Segmentation**: Multi-round interaction with memory
- **Segment Everything**: Automatic segmentation of all objects
- **Client-Server Architecture**: Remote inference support

## Installation

### Prerequisites
```bash
# Basic dependencies
pip install torch torchvision numpy pillow opencv-python matplotlib

# For audio support
pip install openai-whisper

# For SEEM model (install from source)
pip install git+https://github.com/UX-Decoder/Segment-Everything-Everywhere-All-At-Once.git
```

### Model Checkpoints
Download SEEM model checkpoints from the official repository:

```bash
# Create data directory
mkdir -p data

# Download SEEM checkpoints (choose one)
# SEEM Focal-L (recommended)
wget https://huggingface.co/xdecoder/SEEM/resolve/main/seem_focall_v1.pt -O data/seem_focall_v1.pt

# SEEM Focal-T (smaller, faster)
wget https://huggingface.co/xdecoder/SEEM/resolve/main/seem_focalt_v1.pt -O data/seem_focalt_v1.pt

# Download config files
mkdir -p configs/seem
wget https://raw.githubusercontent.com/UX-Decoder/Segment-Everything-Everywhere-All-At-Once/v1.0/configs/seem/seem_focall_lang.yaml -O configs/seem/seem_focall_lang.yaml
```

## Quick Start

### Basic Usage

```python
from vlfm.vlm.seem import SEEM
import numpy as np
from PIL import Image

# Initialize SEEM
seem = SEEM(
    model_checkpoint="data/seem_focall_v1.pt",
    config_path="configs/seem/seem_focall_lang.yaml"
)

# Load image
image = np.array(Image.open("your_image.jpg"))

# Text-based segmentation
mask, labels = seem.segment_with_text(image, "a red car")

# Point-based segmentation  
mask = seem.segment_with_points(image, [[100, 100], [200, 200]], [1, 1])

# Bounding box segmentation
mask = seem.segment_with_bbox(image, [50, 50, 300, 300])

# Combined prompts
mask = seem.segment_with_combined_prompts(
    image,
    points=[[150, 150]],
    text_prompt="person",
    bbox=[100, 100, 400, 400]
)
```

### Client-Server Usage

Start the SEEM server:
```bash
python -m vlfm.vlm.seem --port 12184 \
    --model_checkpoint data/seem_focall_v1.pt \
    --config_path configs/seem/seem_focall_lang.yaml
```

Use the client:
```python
from vlfm.vlm.seem import SEEMClient

client = SEEMClient(port=12184)
mask = client.segment_with_text(image, "a blue car")
```

## API Reference

### SEEM Class

#### `__init__(model_checkpoint, config_path, device=None, model_type="seem_focall_v1")`
Initialize SEEM model.

#### `segment_with_points(image, points, labels=None)`
Segment using point prompts.
- `image`: Input image as numpy array
- `points`: List of [x, y] coordinates  
- `labels`: List of point labels (1=positive, 0=negative)

#### `segment_with_text(image, text_prompt, return_labels=True)`
Segment using text description.
- `image`: Input image as numpy array
- `text_prompt`: Natural language description
- `return_labels`: Whether to return semantic labels

#### `segment_with_bbox(image, bbox)`
Segment using bounding box.
- `image`: Input image as numpy array
- `bbox`: Bounding box as [x1, y1, x2, y2]

#### `segment_with_combined_prompts(image, **kwargs)`
Segment using multiple prompt types.
- Supports: `points`, `bbox`, `text_prompt`, `scribble_mask`, `reference_image`

#### `segment_with_audio(image, audio_path, return_labels=True)`
Segment using audio description (requires Whisper).
- `audio_path`: Path to audio file

#### `interactive_segmentation(image, session_history=None)`
Interactive segmentation with memory.
- `session_history`: Previous interaction history

#### `segment_everything(image, return_labels=True)`
Segment all objects automatically.

### SEEMClient Class

Client for remote SEEM inference. Supports all the same methods as SEEM class.

## Examples

See `seem_example.py` for comprehensive usage examples:

```bash
python vlfm/vlm/seem_example.py
```

## Comparison with SAM

| Feature | SAM | SEEM |
|---------|-----|------|
| Point Prompts | ✅ | ✅ |
| Box Prompts | ✅ | ✅ |
| Text Prompts | ❌ | ✅ |
| Audio Prompts | ❌ | ✅ |
| Reference Images | ❌ | ✅ |
| Combined Prompts | ❌ | ✅ |
| Semantic Labels | ❌ | ✅ |
| Interactive Mode | ❌ | ✅ |

## Performance Notes

- **SEEM Focal-L**: Best accuracy, slower inference (~2-3s per image)
- **SEEM Focal-T**: Faster inference (~0.5-1s per image), slightly lower accuracy
- **GPU Recommended**: CUDA-enabled GPU significantly improves performance
- **Memory Usage**: ~4-8GB GPU memory depending on model size

## Troubleshooting

### Common Issues

1. **Import Error**: Make sure SEEM is properly installed
2. **CUDA Out of Memory**: Use smaller model or reduce batch size
3. **Audio Not Working**: Install Whisper with `pip install openai-whisper`
4. **Server Connection Failed**: Check if server is running on correct port

### Debug Mode

Enable debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## References

- **Paper**: "Segment Everything Everywhere All at Once" (NeurIPS 2023)
- **GitHub**: https://github.com/UX-Decoder/Segment-Everything-Everywhere-All-At-Once
- **Demo**: http://semantic-sam.xyzou.net:6090/
- **Hugging Face**: https://huggingface.co/xdecoder/SEEM

## License

This implementation follows the same license as the original SEEM project (Apache 2.0).

## Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.
