#!/usr/bin/env python3
"""
SEEM Usage Examples

This file demonstrates how to use the SEEM (Segment Everything Everywhere All at Once) 
model for various segmentation tasks with different prompt types.

Requirements:
    pip install torch torchvision numpy pillow
    # For audio support:
    pip install openai-whisper
    # For SEEM model (install from source):
    pip install git+https://github.com/UX-Decoder/Segment-Everything-Everywhere-All-At-Once.git
"""

import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from typing import List, Optional, Tuple

from seem import SEEM, SEEMClient


def load_image(image_path: str) -> np.ndarray:
    """Load image from file path."""
    image = Image.open(image_path).convert('RGB')
    return np.array(image)


def visualize_segmentation(
    image: np.ndarray, 
    mask: np.ndarray, 
    title: str = "Segmentation Result",
    alpha: float = 0.5
) -> None:
    """Visualize segmentation result."""
    plt.figure(figsize=(12, 6))
    
    # Original image
    plt.subplot(1, 2, 1)
    plt.imshow(image)
    plt.title("Original Image")
    plt.axis('off')
    
    # Segmentation overlay
    plt.subplot(1, 2, 2)
    plt.imshow(image)
    
    # Create colored mask
    colored_mask = np.zeros_like(image)
    colored_mask[mask] = [255, 0, 0]  # Red color for mask
    
    plt.imshow(colored_mask, alpha=alpha)
    plt.title(title)
    plt.axis('off')
    
    plt.tight_layout()
    plt.show()


def example_point_segmentation():
    """Example: Segment object using point prompts."""
    print("=== Point-based Segmentation ===")
    
    # Initialize SEEM model
    seem = SEEM(
        model_checkpoint="data/seem_focall_v1.pt",
        config_path="configs/seem/seem_focall_lang.yaml"
    )
    
    # Load image
    image = load_image("example_images/sample.jpg")
    
    # Define points (x, y coordinates)
    points = [[300, 200], [350, 250]]  # Two positive points
    labels = [1, 1]  # Both positive
    
    # Segment with points
    mask = seem.segment_with_points(image, points, labels)
    
    # Visualize result
    visualize_segmentation(image, mask, "Point-based Segmentation")
    
    print(f"Segmented region area: {np.sum(mask)} pixels")


def example_text_segmentation():
    """Example: Segment object using text prompt."""
    print("=== Text-based Segmentation ===")
    
    seem = SEEM(
        model_checkpoint="data/seem_focall_v1.pt",
        config_path="configs/seem/seem_focall_lang.yaml"
    )
    
    image = load_image("example_images/sample.jpg")
    
    # Text prompt
    text_prompt = "a person wearing a red shirt"
    
    # Segment with text
    mask, labels = seem.segment_with_text(image, text_prompt, return_labels=True)
    
    visualize_segmentation(image, mask, f"Text Segmentation: '{text_prompt}'")
    
    print(f"Detected labels: {labels}")
    print(f"Segmented region area: {np.sum(mask)} pixels")


def example_bbox_segmentation():
    """Example: Segment object using bounding box."""
    print("=== Bounding Box Segmentation ===")
    
    seem = SEEM(
        model_checkpoint="data/seem_focall_v1.pt",
        config_path="configs/seem/seem_focall_lang.yaml"
    )
    
    image = load_image("example_images/sample.jpg")
    
    # Bounding box [x1, y1, x2, y2]
    bbox = [100, 100, 400, 300]
    
    # Segment with bbox
    mask = seem.segment_with_bbox(image, bbox)
    
    visualize_segmentation(image, mask, "Bounding Box Segmentation")
    
    print(f"Segmented region area: {np.sum(mask)} pixels")


def example_reference_image_segmentation():
    """Example: Segment using reference image."""
    print("=== Reference Image Segmentation ===")
    
    seem = SEEM(
        model_checkpoint="data/seem_focall_v1.pt",
        config_path="configs/seem/seem_focall_lang.yaml"
    )
    
    target_image = load_image("example_images/target.jpg")
    reference_image = load_image("example_images/reference.jpg")
    
    # Optional: create reference mask to specify region of interest
    # reference_mask = create_reference_mask(reference_image)
    
    # Segment using reference
    mask = seem.segment_with_reference_image(
        target_image, 
        reference_image, 
        reference_mask=None
    )
    
    visualize_segmentation(target_image, mask, "Reference Image Segmentation")
    
    print(f"Segmented region area: {np.sum(mask)} pixels")


def example_combined_prompts():
    """Example: Segment using multiple prompt types."""
    print("=== Combined Prompts Segmentation ===")
    
    seem = SEEM(
        model_checkpoint="data/seem_focall_v1.pt",
        config_path="configs/seem/seem_focall_lang.yaml"
    )
    
    image = load_image("example_images/sample.jpg")
    
    # Combine multiple prompts
    points = [[200, 150]]
    point_labels = [1]
    bbox = [100, 100, 400, 300]
    text_prompt = "car"
    
    mask, labels = seem.segment_with_combined_prompts(
        image,
        points=points,
        point_labels=point_labels,
        bbox=bbox,
        text_prompt=text_prompt,
        return_labels=True
    )
    
    visualize_segmentation(image, mask, "Combined Prompts Segmentation")
    
    print(f"Detected labels: {labels}")
    print(f"Segmented region area: {np.sum(mask)} pixels")


def example_audio_segmentation():
    """Example: Segment using audio prompt."""
    print("=== Audio-based Segmentation ===")
    
    seem = SEEM(
        model_checkpoint="data/seem_focall_v1.pt",
        config_path="configs/seem/seem_focall_lang.yaml"
    )
    
    image = load_image("example_images/sample.jpg")
    
    # Audio file path (should contain speech describing the target object)
    audio_path = "example_audio/description.wav"
    
    try:
        mask, labels = seem.segment_with_audio(image, audio_path, return_labels=True)
        
        visualize_segmentation(image, mask, "Audio-based Segmentation")
        
        print(f"Detected labels: {labels}")
        print(f"Segmented region area: {np.sum(mask)} pixels")
        
    except Exception as e:
        print(f"Audio segmentation failed: {e}")
        print("Make sure Whisper is installed: pip install openai-whisper")


def example_interactive_segmentation():
    """Example: Interactive segmentation with memory."""
    print("=== Interactive Segmentation ===")
    
    seem = SEEM(
        model_checkpoint="data/seem_focall_v1.pt",
        config_path="configs/seem/seem_focall_lang.yaml"
    )
    
    image = load_image("example_images/sample.jpg")
    
    # Start interactive session
    session_history = []
    
    # First interaction
    mask1, session_history = seem.interactive_segmentation(image, session_history)
    print(f"First interaction - Segmented area: {np.sum(mask1)} pixels")
    
    # Second interaction (with memory of first)
    mask2, session_history = seem.interactive_segmentation(image, session_history)
    print(f"Second interaction - Segmented area: {np.sum(mask2)} pixels")
    
    visualize_segmentation(image, mask2, "Interactive Segmentation (Final)")


def example_client_usage():
    """Example: Using SEEM client for remote inference."""
    print("=== Client Usage ===")
    
    # Connect to SEEM server (make sure server is running)
    client = SEEMClient(port=12184)
    
    image = load_image("example_images/sample.jpg")
    
    try:
        # Text-based segmentation via client
        mask, labels = client.segment_with_text(
            image, 
            "a blue car", 
            return_labels=True
        )
        
        visualize_segmentation(image, mask, "Client-based Segmentation")
        
        print(f"Detected labels: {labels}")
        print(f"Segmented region area: {np.sum(mask)} pixels")
        
    except Exception as e:
        print(f"Client connection failed: {e}")
        print("Make sure SEEM server is running on port 12184")


def example_segment_everything():
    """Example: Segment all objects in image."""
    print("=== Segment Everything ===")
    
    seem = SEEM(
        model_checkpoint="data/seem_focall_v1.pt",
        config_path="configs/seem/seem_focall_lang.yaml"
    )
    
    image = load_image("example_images/sample.jpg")
    
    # Segment all objects
    masks, labels = seem.segment_everything(image, return_labels=True)
    
    print(f"Found {len(masks)} objects")
    print(f"Labels: {labels}")
    
    # Visualize first few masks
    for i, (mask, label) in enumerate(zip(masks[:3], labels[:3])):
        visualize_segmentation(image, mask, f"Object {i+1}: {label}")


if __name__ == "__main__":
    print("SEEM Segmentation Examples")
    print("=" * 50)
    
    # Run examples (comment out as needed)
    try:
        example_point_segmentation()
        example_text_segmentation()
        example_bbox_segmentation()
        example_combined_prompts()
        example_reference_image_segmentation()
        example_audio_segmentation()
        example_interactive_segmentation()
        example_segment_everything()
        example_client_usage()
        
    except Exception as e:
        print(f"Example failed: {e}")
        print("\nNote: Make sure you have:")
        print("1. SEEM model checkpoint and config files")
        print("2. Example images in 'example_images/' directory")
        print("3. Required dependencies installed")
        print("4. SEEM server running (for client example)")
