from typing import Any, Optional, List, Union
import base64
import io
import json

import numpy as np
import requests
from PIL import Image


class DAMClient:
    """Describe Anything Model (DAM) Client for sending requests to DAM server."""

    def __init__(self, port: int = 9092, model_name: str = "describe_anything_model"):
        """
        Initialize DAM client.
        
        Args:
            port (int): The port of the DAM server (default: 9092)
            model_name (str): The model name for API requests (default: describe_anything_model)
        """
        self.server_url = f"http://localhost:{port}"
        self.model_name = model_name
        
    def _combine_image_and_mask(self, image: Union[np.ndarray, Image.Image], mask: np.ndarray) -> str:
        """
        Combine image and mask into RGBA format expected by DAM server.
        
        Args:
            image: Input image (RGB)
            mask: Binary mask where 1 indicates region of interest
            
        Returns:
            str: Base64 encoded RGBA image with mask in alpha channel
        """
        if isinstance(image, np.ndarray):
            pil_image = Image.fromarray(image)
        else:
            pil_image = image
            
        # Convert to RGB if needed
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')
            
        # Ensure mask is binary and same size as image
        # Ensure mask is binary and uint8 (0 or 255)
        mask = (mask > 0).astype(np.uint8) * 255
            
        # Resize mask to match image if needed
        if mask.shape[:2] != pil_image.size[::-1]:
            mask_pil = Image.fromarray(mask, mode='L')
            mask_pil = mask_pil.resize(pil_image.size, Image.NEAREST)
            mask = np.array(mask_pil)
        
        # Convert image to numpy array
        image_np = np.array(pil_image)
        
        # Create RGBA image
        rgba_image = np.zeros((image_np.shape[0], image_np.shape[1], 4), dtype=np.uint8)
        rgba_image[:, :, :3] = image_np  # RGB channels
        rgba_image[:, :, 3] = mask       # Alpha channel with mask
        
        # Convert back to PIL and encode
        rgba_pil = Image.fromarray(rgba_image, 'RGBA')
        buffer = io.BytesIO()
        rgba_pil.save(buffer, format="PNG")
        image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        return f"data:image/png;base64,{image_base64}"
    
    def describe_with_mask(self, image: Union[np.ndarray, Image.Image], mask: np.ndarray, 
                          prompt: str = "Describe this image region in detail.") -> str:
        """
        Get description for a specific region of the image using a mask.
        
        Args:
            image (numpy.ndarray or PIL.Image): The input image
            mask (numpy.ndarray): Binary mask indicating the region of interest
            prompt (str): The prompt for description (default: "Describe this image region in detail.")
            
        Returns:
            str: The description text for the specified region
        """
        # print(f"DAMClient.describe_with_mask: image shape {image.shape if isinstance(image, np.ndarray) else image.size}, mask shape {mask.shape}")
        
        # Combine image and mask into RGBA format
        rgba_image_base64 = self._combine_image_and_mask(image, mask)
        
        # Prepare the request payload in OpenAI format
        payload = {
            "model": self.model_name,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": rgba_image_base64
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 512,
            "temperature": 0.2
        }
        
        try:
            response = requests.post(
                f"{self.server_url}/chat/completions",
                headers={"Content-Type": "application/json"},
                json=payload,
                timeout=60
            )
            response.raise_for_status()
            
            result = response.json()
            
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                raise ValueError(f"Unexpected response format: {result}")
                
        except requests.exceptions.RequestException as e:
            raise RuntimeError(f"Failed to get description from DAM server: {e}")
    
    def describe_full_image(self, image: Union[np.ndarray, Image.Image], 
                           prompt: str = "Describe this image in detail.") -> str:
        """
        Get description for the full image.
        
        Args:
            image (numpy.ndarray or PIL.Image): The input image
            prompt (str): The prompt for description
            
        Returns:
            str: The description text for the full image
        """
        # print(f"DAMClient.describe_full_image: image shape {image.shape if isinstance(image, np.ndarray) else image.size}")
        
        # Create a full mask (all ones) for the entire image
        if isinstance(image, np.ndarray):
            h, w = image.shape[:2]
        else:
            w, h = image.size
        
        full_mask = np.ones((h, w), dtype=np.uint8) * 255
        
        # Use the same logic as describe_with_mask
        return self.describe_with_mask(image, full_mask, prompt)
    
    def check_server_status(self) -> bool:
        """
        Check if the DAM server is running and accessible.
        
        Returns:
            bool: True if server is accessible, False otherwise
        """
        try:
            # Create a simple test image
            test_image = np.ones((100, 100, 3), dtype=np.uint8) * 128  # Gray image
            test_mask = np.ones((100, 100), dtype=np.uint8) * 255      # Full mask
            
            rgba_image_base64 = self._combine_image_and_mask(test_image, test_mask)
            
            test_payload = {
                "model": self.model_name,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "test"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": rgba_image_base64
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 1
            }
            response = requests.post(
                f"{self.server_url}/chat/completions",
                headers={"Content-Type": "application/json"},
                json=test_payload,
                timeout=10
            )
            # Even if the request fails due to model processing, 
            # a 200 or 500 status means the server is running and accessible
            return response.status_code in [200, 500]
        except requests.exceptions.RequestException:
            return False


if __name__ == "__main__":
    import argparse
    import os

    parser = argparse.ArgumentParser(description="Test DAM Client")
    parser.add_argument("--server_url", type=str, default="http://localhost:9092", help="DAM server URL")
    parser.add_argument("--image_path", type=str, required=True, help="Path to the input image")
    parser.add_argument("--mask_path", type=str, default=None, help="Optional path to the input mask (grayscale or binary PNG)")
    # parser.add_argument("--prompt", type=str, default="Describe this image region in detail.", help="Prompt for the description")
    parser.add_argument("--prompt", type=str, default="Describe the object in the center of the image.", help="Prompt for the description")
    args = parser.parse_args()

    client = DAMClient(port=int(os.environ.get("DAM_PORT", "9092")))

    print(f"Checking DAM server status at {args.server_url}...")
    if client.check_server_status():
        print("DAM server is accessible.")
    else:
        print(f"DAM server at {args.server_url} not accessible! Exiting.")
        exit(1)

    if not os.path.exists(args.image_path):
        print(f"Image path {args.image_path} does not exist. Exiting.")
        exit(1)

    try:
        image = Image.open(args.image_path).convert('RGB')
        print(f"Loaded image from {args.image_path}")

        if args.mask_path:
            if not os.path.exists(args.mask_path):
                print(f"Mask path {args.mask_path} does not exist. Exiting.")
                exit(1)
            mask_image = Image.open(args.mask_path).convert('L') # Ensure grayscale
            mask_np = np.array(mask_image)
            # Ensure mask is binary 0 or 255 for _combine_image_and_mask
            mask_np = ((mask_np > 128) * 255).astype(np.uint8) 
            print(f"Loaded mask from {args.mask_path}")
            description = client.describe_with_mask(image, mask_np, prompt=args.prompt)
            print(f"\nDescription (with mask):\n{description}")
        else:
            description = client.describe_full_image(image, prompt=args.prompt)
            print(f"\nDescription (full image):\n{description}")

    except FileNotFoundError as e:
        print(f"Error: {e}")
    except RuntimeError as e:
        print(f"Runtime error: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
