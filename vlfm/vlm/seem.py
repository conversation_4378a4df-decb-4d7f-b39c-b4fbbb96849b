# Copyright (c) 2023 Boston Dynamics AI Institute LLC. All rights reserved.

"""
SEEM (Segment Everything Everywhere All at Once) Implementation

This module provides a wrapper for the SEEM model, which supports multi-modal
segmentation with various prompt types including:

- Visual prompts: points, bounding boxes, scribbles, masks
- Text prompts: natural language descriptions
- Image prompts: reference images for similar object segmentation
- Audio prompts: speech converted to text via Whisper
- Combined prompts: any combination of the above
- Interactive segmentation: multi-round interaction with memory

Usage Examples:

    # Basic usage with different prompt types
    seem = SEEM(model_checkpoint="seem_focall_v1.pt", config_path="config.yaml")

    # Point-based segmentation
    mask = seem.segment_with_points(image, [[100, 100], [200, 200]], [1, 1])

    # Text-based segmentation
    mask, labels = seem.segment_with_text(image, "a red car")

    # Combined prompts
    mask = seem.segment_with_combined_prompts(
        image,
        points=[[100, 100]],
        text_prompt="car",
        bbox=[50, 50, 300, 300]
    )

    # Client usage
    client = SEEMClient(port=12184)
    mask = client.segment_with_text(image, "person")

References:
    - Paper: "Segment Everything Everywhere All at Once" (NeurIPS 2023)
    - GitHub: https://github.com/UX-Decoder/Segment-Everything-Everywhere-All-At-Once
"""

import os
from typing import Any, Dict, List, Optional, Union, Tuple
import numpy as np
import torch
from PIL import Image

from .server_wrapper import (
    ServerMixin,
    bool_arr_to_str,
    host_model,
    send_request,
    str_to_bool_arr,
    str_to_image,
)

try:
    # SEEM imports - these would be the actual SEEM dependencies
    # from seem import build_seem_model, SeemPredictor
    # For now, we'll create placeholder classes
    print("SEEM dependencies not found. Using placeholder implementation.")
    build_seem_model = None
    SeemPredictor = None
except ModuleNotFoundError:
    print("Could not import SEEM. This is OK if you are only using the client.")
    build_seem_model = None
    SeemPredictor = None


class SEEM:
    """
    SEEM (Segment Everything Everywhere All at Once) model wrapper.
    
    Supports multiple prompt types:
    - Visual prompts: points, boxes, scribbles, masks
    - Text prompts: natural language descriptions
    - Image prompts: reference images for similar object segmentation
    - Audio prompts: speech converted to text
    """
    
    def __init__(
        self,
        model_checkpoint: str,
        config_path: str,
        device: Optional[Any] = None,
        model_type: str = "seem_focall_v1",
    ) -> None:
        if device is None:
            device = torch.device("cuda") if torch.cuda.is_available() else "cpu"
        self.device = device
        self.model_type = model_type
        
        # Initialize SEEM model
        if build_seem_model is not None:
            self.model = build_seem_model(config_path, model_checkpoint)
            self.model.to(device=device)
            self.model.eval()
            self.predictor = SeemPredictor(self.model)
        else:
            # Placeholder for when SEEM is not available
            self.model = None
            self.predictor = None
            print("Warning: SEEM model not loaded. Using placeholder.")

    def segment_with_points(
        self, 
        image: np.ndarray, 
        points: List[List[int]], 
        labels: Optional[List[int]] = None
    ) -> np.ndarray:
        """
        Segment objects using point prompts.
        
        Args:
            image: Input image as numpy array
            points: List of [x, y] coordinates
            labels: List of point labels (1 for positive, 0 for negative)
            
        Returns:
            Segmentation mask as boolean numpy array
        """
        if self.predictor is None:
            return self._placeholder_segmentation(image)
            
        with torch.inference_mode():
            self.predictor.set_image(image)
            if labels is None:
                labels = [1] * len(points)  # Default to positive points
            
            masks, scores, _ = self.predictor.predict(
                point_coords=np.array(points),
                point_labels=np.array(labels),
                multimask_output=False
            )
            
        return masks[0]

    def segment_with_bbox(self, image: np.ndarray, bbox: List[int]) -> np.ndarray:
        """
        Segment object using bounding box prompt.
        
        Args:
            image: Input image as numpy array
            bbox: Bounding box as [x1, y1, x2, y2]
            
        Returns:
            Segmentation mask as boolean numpy array
        """
        if self.predictor is None:
            return self._placeholder_segmentation(image)
            
        with torch.inference_mode():
            self.predictor.set_image(image)
            masks, scores, _ = self.predictor.predict(
                box=np.array(bbox),
                multimask_output=False
            )
            
        return masks[0]

    def segment_with_text(
        self, 
        image: np.ndarray, 
        text_prompt: str,
        return_labels: bool = True
    ) -> Union[np.ndarray, Tuple[np.ndarray, List[str]]]:
        """
        Segment objects using text prompt (grounding segmentation).
        
        Args:
            image: Input image as numpy array
            text_prompt: Natural language description of target object
            return_labels: Whether to return semantic labels
            
        Returns:
            Segmentation mask, optionally with semantic labels
        """
        if self.predictor is None:
            mask = self._placeholder_segmentation(image)
            if return_labels:
                return mask, [text_prompt]
            return mask
            
        with torch.inference_mode():
            self.predictor.set_image(image)
            results = self.predictor.predict_with_text(
                text_prompt=text_prompt,
                multimask_output=False
            )
            
            masks = results.get('masks', [])
            labels = results.get('labels', [text_prompt])
            
            if len(masks) > 0:
                mask = masks[0]
            else:
                mask = np.zeros(image.shape[:2], dtype=bool)
                
        if return_labels:
            return mask, labels
        return mask

    def segment_with_scribble(
        self, 
        image: np.ndarray, 
        scribble_mask: np.ndarray,
        scribble_label: int = 1
    ) -> np.ndarray:
        """
        Segment objects using scribble prompts.
        
        Args:
            image: Input image as numpy array
            scribble_mask: Binary mask indicating scribble locations
            scribble_label: Label for scribble (1 for positive, 0 for negative)
            
        Returns:
            Segmentation mask as boolean numpy array
        """
        if self.predictor is None:
            return self._placeholder_segmentation(image)
            
        with torch.inference_mode():
            self.predictor.set_image(image)
            masks, scores, _ = self.predictor.predict_with_scribble(
                scribble_mask=scribble_mask,
                scribble_label=scribble_label,
                multimask_output=False
            )
            
        return masks[0]

    def segment_with_reference_image(
        self, 
        target_image: np.ndarray,
        reference_image: np.ndarray,
        reference_mask: Optional[np.ndarray] = None
    ) -> np.ndarray:
        """
        Segment objects in target image using reference image.
        
        Args:
            target_image: Image to segment
            reference_image: Reference image containing similar objects
            reference_mask: Optional mask indicating region of interest in reference
            
        Returns:
            Segmentation mask as boolean numpy array
        """
        if self.predictor is None:
            return self._placeholder_segmentation(target_image)
            
        with torch.inference_mode():
            masks, scores, _ = self.predictor.predict_with_reference(
                target_image=target_image,
                reference_image=reference_image,
                reference_mask=reference_mask,
                multimask_output=False
            )
            
        return masks[0]

    def segment_everything(
        self, 
        image: np.ndarray,
        return_labels: bool = True
    ) -> Union[List[np.ndarray], Tuple[List[np.ndarray], List[str]]]:
        """
        Segment all objects in the image automatically.
        
        Args:
            image: Input image as numpy array
            return_labels: Whether to return semantic labels for each mask
            
        Returns:
            List of segmentation masks, optionally with labels
        """
        if self.predictor is None:
            masks = [self._placeholder_segmentation(image)]
            if return_labels:
                return masks, ["placeholder_object"]
            return masks
            
        with torch.inference_mode():
            self.predictor.set_image(image)
            results = self.predictor.generate_everything()
            
            masks = results.get('masks', [])
            labels = results.get('labels', []) if return_labels else None
            
        if return_labels and labels:
            return masks, labels
        return masks

    def segment_with_audio(
        self,
        image: np.ndarray,
        audio_path: str,
        return_labels: bool = True
    ) -> Union[np.ndarray, Tuple[np.ndarray, List[str]]]:
        """
        Segment objects using audio prompt (converted to text via Whisper).

        Args:
            image: Input image as numpy array
            audio_path: Path to audio file
            return_labels: Whether to return semantic labels

        Returns:
            Segmentation mask, optionally with semantic labels
        """
        try:
            import whisper
            # Load Whisper model for audio-to-text conversion
            whisper_model = whisper.load_model("base")
            result = whisper_model.transcribe(audio_path)
            text_prompt = result["text"].strip()
            print(f"Audio transcribed to: {text_prompt}")

            # Use text segmentation with transcribed text
            return self.segment_with_text(image, text_prompt, return_labels)

        except ImportError:
            print("Whisper not available. Please install: pip install openai-whisper")
            mask = self._placeholder_segmentation(image)
            if return_labels:
                return mask, ["audio_placeholder"]
            return mask

    def segment_with_combined_prompts(
        self,
        image: np.ndarray,
        points: Optional[List[List[int]]] = None,
        point_labels: Optional[List[int]] = None,
        bbox: Optional[List[int]] = None,
        text_prompt: Optional[str] = None,
        scribble_mask: Optional[np.ndarray] = None,
        reference_image: Optional[np.ndarray] = None,
        reference_mask: Optional[np.ndarray] = None,
        return_labels: bool = True
    ) -> Union[np.ndarray, Tuple[np.ndarray, List[str]]]:
        """
        Segment objects using combination of multiple prompt types.

        Args:
            image: Input image as numpy array
            points: Optional list of [x, y] coordinates
            point_labels: Optional list of point labels
            bbox: Optional bounding box as [x1, y1, x2, y2]
            text_prompt: Optional natural language description
            scribble_mask: Optional scribble mask
            reference_image: Optional reference image
            reference_mask: Optional reference mask
            return_labels: Whether to return semantic labels

        Returns:
            Segmentation mask, optionally with semantic labels
        """
        if self.predictor is None:
            mask = self._placeholder_segmentation(image)
            if return_labels:
                labels = [text_prompt] if text_prompt else ["combined_placeholder"]
                return mask, labels
            return mask

        with torch.inference_mode():
            self.predictor.set_image(image)

            # Prepare combined prompts
            prompt_dict = {}
            if points is not None:
                prompt_dict['point_coords'] = np.array(points)
                if point_labels is not None:
                    prompt_dict['point_labels'] = np.array(point_labels)
                else:
                    prompt_dict['point_labels'] = np.ones(len(points))

            if bbox is not None:
                prompt_dict['box'] = np.array(bbox)

            if text_prompt is not None:
                prompt_dict['text_prompt'] = text_prompt

            if scribble_mask is not None:
                prompt_dict['scribble_mask'] = scribble_mask

            if reference_image is not None:
                prompt_dict['reference_image'] = reference_image
                if reference_mask is not None:
                    prompt_dict['reference_mask'] = reference_mask

            # Predict with combined prompts
            results = self.predictor.predict_combined(
                **prompt_dict,
                multimask_output=False
            )

            masks = results.get('masks', [])
            labels = results.get('labels', []) if return_labels else None

            if len(masks) > 0:
                mask = masks[0]
            else:
                mask = np.zeros(image.shape[:2], dtype=bool)

        if return_labels and labels:
            return mask, labels
        return mask

    def interactive_segmentation(
        self,
        image: np.ndarray,
        session_history: Optional[List[Dict]] = None
    ) -> Tuple[np.ndarray, List[Dict]]:
        """
        Interactive segmentation with memory of previous interactions.

        Args:
            image: Input image as numpy array
            session_history: List of previous interactions

        Returns:
            Tuple of (segmentation mask, updated session history)
        """
        if session_history is None:
            session_history = []

        if self.predictor is None:
            mask = self._placeholder_segmentation(image)
            session_history.append({
                'type': 'placeholder',
                'mask': mask,
                'timestamp': torch.cuda.Event().record() if torch.cuda.is_available() else None
            })
            return mask, session_history

        with torch.inference_mode():
            self.predictor.set_image(image)

            # Use session history for context-aware prediction
            results = self.predictor.predict_interactive(
                session_history=session_history,
                multimask_output=False
            )

            masks = results.get('masks', [])
            if len(masks) > 0:
                mask = masks[0]
                # Update session history
                session_history.append({
                    'type': 'interactive',
                    'mask': mask,
                    'confidence': results.get('confidence', 0.0),
                    'timestamp': torch.cuda.Event().record() if torch.cuda.is_available() else None
                })
            else:
                mask = np.zeros(image.shape[:2], dtype=bool)

        return mask, session_history

    def _placeholder_segmentation(self, image: np.ndarray) -> np.ndarray:
        """Create a placeholder segmentation mask for testing."""
        h, w = image.shape[:2]
        # Create a simple rectangular mask in the center
        mask = np.zeros((h, w), dtype=bool)
        mask[h//4:3*h//4, w//4:3*w//4] = True
        return mask


class SEEMClient:
    """Client for SEEM model server."""
    
    def __init__(self, port: int = 12184):
        self.url = f"http://localhost:{port}/seem"

    def segment_with_points(
        self, 
        image: np.ndarray, 
        points: List[List[int]], 
        labels: Optional[List[int]] = None
    ) -> np.ndarray:
        response = send_request(
            self.url + "/points", 
            image=image, 
            points=points, 
            labels=labels
        )
        mask_str = response["mask"]
        mask = str_to_bool_arr(mask_str, shape=tuple(image.shape[:2]))
        return mask

    def segment_with_bbox(self, image: np.ndarray, bbox: List[int]) -> np.ndarray:
        response = send_request(self.url + "/bbox", image=image, bbox=bbox)
        mask_str = response["mask"]
        mask = str_to_bool_arr(mask_str, shape=tuple(image.shape[:2]))
        return mask

    def segment_with_text(
        self, 
        image: np.ndarray, 
        text_prompt: str,
        return_labels: bool = True
    ) -> Union[np.ndarray, Tuple[np.ndarray, List[str]]]:
        response = send_request(
            self.url + "/text", 
            image=image, 
            text_prompt=text_prompt,
            return_labels=return_labels
        )
        mask_str = response["mask"]
        mask = str_to_bool_arr(mask_str, shape=tuple(image.shape[:2]))
        
        if return_labels and "labels" in response:
            return mask, response["labels"]
        return mask

    def segment_with_reference_image(
        self,
        target_image: np.ndarray,
        reference_image: np.ndarray,
        reference_mask: Optional[np.ndarray] = None
    ) -> np.ndarray:
        response = send_request(
            self.url + "/reference",
            target_image=target_image,
            reference_image=reference_image,
            reference_mask=reference_mask
        )
        mask_str = response["mask"]
        mask = str_to_bool_arr(mask_str, shape=tuple(target_image.shape[:2]))
        return mask

    def segment_with_audio(
        self,
        image: np.ndarray,
        audio_path: str,
        return_labels: bool = True
    ) -> Union[np.ndarray, Tuple[np.ndarray, List[str]]]:
        response = send_request(
            self.url + "/audio",
            image=image,
            audio_path=audio_path,
            return_labels=return_labels
        )
        mask_str = response["mask"]
        mask = str_to_bool_arr(mask_str, shape=tuple(image.shape[:2]))

        if return_labels and "labels" in response:
            return mask, response["labels"]
        return mask

    def segment_with_combined_prompts(
        self,
        image: np.ndarray,
        points: Optional[List[List[int]]] = None,
        point_labels: Optional[List[int]] = None,
        bbox: Optional[List[int]] = None,
        text_prompt: Optional[str] = None,
        scribble_mask: Optional[np.ndarray] = None,
        reference_image: Optional[np.ndarray] = None,
        reference_mask: Optional[np.ndarray] = None,
        return_labels: bool = True
    ) -> Union[np.ndarray, Tuple[np.ndarray, List[str]]]:
        response = send_request(
            self.url + "/combined",
            image=image,
            points=points,
            point_labels=point_labels,
            bbox=bbox,
            text_prompt=text_prompt,
            scribble_mask=scribble_mask,
            reference_image=reference_image,
            reference_mask=reference_mask,
            return_labels=return_labels
        )
        mask_str = response["mask"]
        mask = str_to_bool_arr(mask_str, shape=tuple(image.shape[:2]))

        if return_labels and "labels" in response:
            return mask, response["labels"]
        return mask

    def interactive_segmentation(
        self,
        image: np.ndarray,
        session_history: Optional[List[Dict]] = None
    ) -> Tuple[np.ndarray, List[Dict]]:
        response = send_request(
            self.url + "/interactive",
            image=image,
            session_history=session_history
        )
        mask_str = response["mask"]
        mask = str_to_bool_arr(mask_str, shape=tuple(image.shape[:2]))
        updated_history = response.get("session_history", [])

        return mask, updated_history


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument("--port", type=int, default=12184)
    parser.add_argument("--model_checkpoint", type=str, 
                       default=os.environ.get("SEEM_CHECKPOINT", "data/seem_focall_v1.pt"))
    parser.add_argument("--config_path", type=str,
                       default=os.environ.get("SEEM_CONFIG", "configs/seem/seem_focall_lang.yaml"))
    args = parser.parse_args()

    print("Loading SEEM model...")

    class SEEMServer(ServerMixin, SEEM):
        def process_payload(self, payload: dict) -> dict:
            endpoint = payload.get("endpoint", "points")
            
            if endpoint == "points":
                image = str_to_image(payload["image"])
                points = payload["points"]
                labels = payload.get("labels")
                mask = self.segment_with_points(image, points, labels)
                mask_str = bool_arr_to_str(mask)
                return {"mask": mask_str}
                
            elif endpoint == "bbox":
                image = str_to_image(payload["image"])
                bbox = payload["bbox"]
                mask = self.segment_with_bbox(image, bbox)
                mask_str = bool_arr_to_str(mask)
                return {"mask": mask_str}
                
            elif endpoint == "text":
                image = str_to_image(payload["image"])
                text_prompt = payload["text_prompt"]
                return_labels = payload.get("return_labels", True)
                
                if return_labels:
                    mask, labels = self.segment_with_text(image, text_prompt, return_labels)
                    mask_str = bool_arr_to_str(mask)
                    return {"mask": mask_str, "labels": labels}
                else:
                    mask = self.segment_with_text(image, text_prompt, return_labels)
                    mask_str = bool_arr_to_str(mask)
                    return {"mask": mask_str}
                    
            elif endpoint == "reference":
                target_image = str_to_image(payload["target_image"])
                reference_image = str_to_image(payload["reference_image"])
                reference_mask = payload.get("reference_mask")
                if reference_mask:
                    reference_mask = str_to_bool_arr(reference_mask,
                                                   shape=tuple(reference_image.shape[:2]))

                mask = self.segment_with_reference_image(
                    target_image, reference_image, reference_mask
                )
                mask_str = bool_arr_to_str(mask)
                return {"mask": mask_str}

            elif endpoint == "audio":
                image = str_to_image(payload["image"])
                audio_path = payload["audio_path"]
                return_labels = payload.get("return_labels", True)

                if return_labels:
                    mask, labels = self.segment_with_audio(image, audio_path, return_labels)
                    mask_str = bool_arr_to_str(mask)
                    return {"mask": mask_str, "labels": labels}
                else:
                    mask = self.segment_with_audio(image, audio_path, return_labels)
                    mask_str = bool_arr_to_str(mask)
                    return {"mask": mask_str}

            elif endpoint == "combined":
                image = str_to_image(payload["image"])
                points = payload.get("points")
                point_labels = payload.get("point_labels")
                bbox = payload.get("bbox")
                text_prompt = payload.get("text_prompt")
                scribble_mask = payload.get("scribble_mask")
                reference_image = payload.get("reference_image")
                reference_mask = payload.get("reference_mask")
                return_labels = payload.get("return_labels", True)

                if reference_image:
                    reference_image = str_to_image(reference_image)
                if reference_mask:
                    reference_mask = str_to_bool_arr(reference_mask,
                                                   shape=tuple(reference_image.shape[:2]))
                if scribble_mask:
                    scribble_mask = str_to_bool_arr(scribble_mask,
                                                  shape=tuple(image.shape[:2]))

                if return_labels:
                    mask, labels = self.segment_with_combined_prompts(
                        image, points, point_labels, bbox, text_prompt,
                        scribble_mask, reference_image, reference_mask, return_labels
                    )
                    mask_str = bool_arr_to_str(mask)
                    return {"mask": mask_str, "labels": labels}
                else:
                    mask = self.segment_with_combined_prompts(
                        image, points, point_labels, bbox, text_prompt,
                        scribble_mask, reference_image, reference_mask, return_labels
                    )
                    mask_str = bool_arr_to_str(mask)
                    return {"mask": mask_str}

            elif endpoint == "interactive":
                image = str_to_image(payload["image"])
                session_history = payload.get("session_history", [])

                mask, updated_history = self.interactive_segmentation(image, session_history)
                mask_str = bool_arr_to_str(mask)
                return {"mask": mask_str, "session_history": updated_history}

            else:
                raise ValueError(f"Unknown endpoint: {endpoint}")

    seem_server = SEEMServer(
        model_checkpoint=args.model_checkpoint,
        config_path=args.config_path
    )
    print("SEEM model loaded!")
    print(f"Hosting on port {args.port}...")
    host_model(seem_server, name="seem", port=args.port)
