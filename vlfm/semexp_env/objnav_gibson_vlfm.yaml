# Copyright (c) 2023 Boston Dynamics AI Institute LLC. All rights reserved.

ENVIRONMENT:
  MAX_EPISODE_STEPS: 500
SIMULATOR:
  TURN_ANGLE: 30
  TILT_ANGLE: 30
  ACTION_SPACE_CONFIG: "v1"
  AGENT_0:
    SENSORS: ['RGB_SENSOR', 'DEPTH_SENSOR', 'SEMA<PERSON><PERSON>_SENSOR']
    HEIGHT: 0.88
    RADIUS: 0.18
  HABITAT_SIM_V0:
    GPU_DEVICE_ID: 0
    ALLOW_SLIDING: True
  SEMANTIC_SENSOR:
    WIDTH: 640
    HEIGHT: 480
    HFOV: 79
    POSITION: [0, 0.88, 0]
  RGB_SENSOR:
    WIDTH: 640
    HEIGHT: 480
    HFOV: 79
    POSITION: [0, 0.88, 0]
  DEPTH_SENSOR:
    WIDTH: 640
    HEIGHT: 480
    HFOV: 79
    MIN_DEPTH: 0.5
    MAX_DEPTH: 5.0
    POSITION: [0, 0.88, 0]
TASK:
  TYPE: ObjectNav-v1
  POSSIBLE_ACTIONS: ["STOP", "<PERSON>OVE_FORWARD", "TURN_LEFT", "TURN_RIGHT", "<PERSON>O<PERSON>_UP", "<PERSON>OOK_DOWN"]
  SENSORS: ['GPS_SENSOR', 'COMPASS_SENSOR', 'HEADING_SENSOR']
  MEASUREMENTS: ['DISTANCE_TO_GOAL', 'SUCCESS', 'SPL']
  SUCCESS:
    SUCCESS_DISTANCE: 0.2

DATASET:
  TYPE: PointNav-v1
  SPLIT: train
  DATA_PATH: "data/datasets/objectnav/gibson/v1/{split}/{split}.json.gz"
  EPISODES_DIR: "data/datasets/objectnav/gibson/v1/{split}/"
  SCENES_DIR: "data/scene_datasets/"
